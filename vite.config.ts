import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    proxy: {
      // Proxy API requests to avoid CORS issues
      '/api': {
        target: 'https://ene-reverse-proxy-v1.acc.api-digital.enecogroup.com',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path.replace(/^\/api/, '/v1/bot/public/eneco'),
        headers: {
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept': 'text/plain, application/json, */*',
          'Accept-Language': 'en-US,en;q=0.9',
          'Accept-Encoding': 'gzip, deflate, br',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        },
        configure: (proxy, _options) => {
          proxy.on('error', (err, req, res) => {
            console.log('🚨 Proxy Error:', err);
            console.log('Request URL:', req.url);
          });
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('🚀 Sending Request to Target:');
            console.log('  Method:', req.method);
            console.log('  Original URL:', req.url);
            console.log('  Target URL:', proxyReq.path);
            console.log('  Headers:', JSON.stringify(proxyReq.getHeaders(), null, 2));
          });
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('📥 Received Response from Target:');
            console.log('  Status:', proxyRes.statusCode);
            console.log('  URL:', req.url);
            console.log('  Response Headers:', JSON.stringify(proxyRes.headers, null, 2));
          });
        },
      }
    }
  }
})
