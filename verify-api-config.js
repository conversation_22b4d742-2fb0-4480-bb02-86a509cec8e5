// Simple verification script to check API configuration
import chatService from './src/services/chatService.ts';

console.log('🔍 Verifying API Configuration...');
console.log('Environment Variables:');
console.log('- VITE_API_BASE_URL:', process.env.VITE_API_BASE_URL || 'Not set');
console.log('- VITE_USE_MOCK_API:', process.env.VITE_USE_MOCK_API || 'Not set');

// Check which service is being used
console.log('\n📡 Chat Service Configuration:');
console.log('- Service type:', chatService.constructor.name);
console.log('- Is Mock Service:', chatService.constructor.name === 'MockChatService');
console.log('- Is Real Service:', chatService.constructor.name === 'ChatServiceImpl');

if (chatService.constructor.name === 'ChatServiceImpl') {
  console.log('✅ SUCCESS: Real API service is configured and will make HTTP requests to /chat endpoint');
} else {
  console.log('❌ WARNING: Mock service is still being used');
}
