# CORS Troubleshooting Guide

## 🚫 The Problem
CORS (Cross-Origin Resource Sharing) errors occur when a web application running on one domain tries to make requests to a different domain. This is a browser security feature that cannot be bypassed on the client side.

## ✅ Solutions Implemented

### 1. Development Solution: Vite Proxy
We've configured Vite to proxy API requests in development to avoid CORS issues:

**Configuration in `vite.config.ts`:**
```typescript
server: {
  proxy: {
    '/api': {
      target: 'https://bot-v1.acc.api-digital.enecogroup.com',
      changeOrigin: true,
      secure: true
    }
  }
}
```

**How it works:**
- Your frontend makes requests to `/api/eneco/chat`
- Vite proxy forwards them to `https://bot-v1.acc.api-digital.enecogroup.com/api/eneco/chat`
- No CORS issues because the browser sees it as a same-origin request

### 2. Environment Configuration
- **Development**: Uses proxy (`VITE_API_BASE_URL=/api/eneco`)
- **Production**: Uses direct URL (requires server-side CORS configuration)

## 🛠️ Server-Side Solutions (Recommended)

The backend server needs to add these CORS headers:

```http
Access-Control-Allow-Origin: https://your-frontend-domain.com
Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
Access-Control-Allow-Headers: Content-Type, Authorization, X-API-Key
Access-Control-Allow-Credentials: true
```

## 🔧 Alternative Client-Side Workarounds

### Option 1: Browser Extensions (Development Only)
Install a CORS browser extension like "CORS Unblock" - **NOT recommended for production**

### Option 2: Chrome with Disabled Security (Development Only)
Launch Chrome with disabled web security:
```bash
# macOS/Linux
google-chrome --disable-web-security --user-data-dir="/tmp/chrome_dev"

# Windows
chrome.exe --disable-web-security --user-data-dir="c:\temp\chrome_dev"
```
**⚠️ WARNING: Never use this for regular browsing - it disables important security features**

### Option 3: Use a CORS Proxy Service (Not Recommended)
Services like `cors-anywhere.herokuapp.com` - but these are unreliable and not suitable for production.

## 🚀 Testing the Current Setup

1. **Development**: Start the dev server and test - should work with the proxy
2. **Production**: Deploy and ensure the backend has proper CORS headers

## 📝 Next Steps

1. **Contact Backend Team**: Ask them to add CORS headers for your frontend domain
2. **Test Proxy**: Try sending a message in development to verify the proxy works
3. **Monitor Console**: Check browser dev tools for any remaining CORS or network errors

## 🔍 Debugging Tips

- Check browser Network tab to see actual request URLs
- Look for preflight OPTIONS requests
- Verify the proxy is working by checking console logs
- Test with curl to isolate frontend vs backend issues
