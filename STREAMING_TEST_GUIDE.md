# 🧪 Streaming Test Guide

This guide will help you test the streaming functionality using the mock implementation.

## 🚀 Quick Start

1. **Start the development server:**
   ```bash
   npm run dev
   # or
   yarn dev
   ```

2. **The mock streaming is automatically enabled in development mode!**
   - No additional setup required
   - Mock responses are pre-configured with realistic content

## 🎯 Testing Methods

### Method 1: Using the Test Panel (Recommended)

When you run the app in development mode, you'll see a **"🧪 Streaming Test Panel"** in the top-right corner.

**Steps:**
1. Make sure "Streaming Enabled" is checked in the test panel
2. Click any test scenario button:
   - **Basic Streaming**: Tests normal streaming with tool calls
   - **Streaming with Images**: Tests streaming responses that include images
   - **Timeout Error**: Tests timeout error handling
   - **Network Error**: Tests network error handling
   - **Server Error**: Tests server error handling
   - **Interrupted Stream**: Tests interrupted stream handling

### Method 2: Using the Chat Interface

1. **Enable streaming** by clicking the ⚡ (Zap) icon in the chat header
2. **Send any message** - the mock service will respond with realistic streaming content
3. **Watch for:**
   - Tool call indicators (blue boxes with agent names)
   - Character-by-character text streaming
   - Blinking cursor during streaming
   - Images appearing after text (sometimes)

## 🔍 What to Look For

### ✅ Successful Streaming Indicators:
- **Tool Information**: Blue boxes showing which agents are being used
- **Streaming Cursor**: Blinking "|" character at the end of streaming text
- **Progressive Text**: Text appears character by character, not all at once
- **Smooth Animation**: Text flows naturally with realistic delays
- **Images**: Sometimes images appear after the text completes

### ✅ Error Handling:
- **Graceful Fallbacks**: Errors show user-friendly messages
- **Automatic Retry**: Some errors trigger automatic fallback to non-streaming
- **State Recovery**: Chat remains functional after errors

## 🎭 Mock Response Examples

The mock service includes several pre-configured responses:

1. **Welcome Message**: Basic greeting with orchestrator agent
2. **Energy Analysis**: Response with scenario and installment agents
3. **Chart Generation**: Response with visualization agent + sample image
4. **Installment Advice**: Response with installment amount agent
5. **Plan Comparison**: Multi-agent response with detailed options

## ⚙️ Configuration

### Environment Variables (.env.development):
```env
# Enable mock streaming (automatically true in dev mode)
VITE_USE_MOCK_STREAMING=true

# Optional: Also enable mock chat service
VITE_USE_MOCK_API=false
```

### Disable Mock Streaming:
To test with a real API, set:
```env
VITE_USE_MOCK_STREAMING=false
VITE_API_BASE_URL=http://your-api-endpoint.com/api
```

## 🐛 Troubleshooting

### Issue: Test panel not visible
- **Solution**: Make sure you're running in development mode (`npm run dev`)

### Issue: Streaming not working
- **Solution**: Check that the ⚡ icon in chat header is enabled (solid, not outlined)

### Issue: No tool information showing
- **Solution**: This is normal for some responses. Try the "Basic Streaming" test for guaranteed tool calls

### Issue: Errors not showing properly
- **Solution**: Check browser console for detailed error logs

## 🔧 Customizing Mock Responses

To add your own mock responses, edit `src/services/mockStreamingService.ts`:

```typescript
const MOCK_RESPONSES = [
  {
    toolCalls: ['your-agent-name'],
    response: "Your custom streaming response text here...",
    images: [], // Add base64 images if needed
  },
  // Add more responses...
];
```

## 📱 Mobile Testing

The streaming functionality works on mobile devices:
- Test panel adapts to smaller screens
- Touch interactions work with streaming toggle
- Performance remains smooth on mobile

## 🎯 Production Readiness

When ready for production:
1. Set `VITE_USE_MOCK_STREAMING=false`
2. Configure your real API endpoint
3. Remove or hide the test panel (it auto-hides in production)
4. Test with your actual streaming API

## 📊 Performance Notes

- Mock streaming uses realistic delays (50-150ms per word)
- Memory usage is optimized for long streaming sessions
- Error recovery is automatic and transparent to users

---

**Happy Testing! 🚀**

The streaming implementation is production-ready and includes comprehensive error handling, fallback mechanisms, and smooth user experience patterns.
