import ChatContainer from './components/Chat/ChatContainer'
import FloatingChatButton from './components/Chat/FloatingChatButton'
import Homepage from './components/Homepage/Homepage'
import QueryProvider from './providers/QueryProvider'
import StreamingTestPanel from './components/StreamingTestPanel'
import { useChatStore } from './store'
import { VIEW_MODE } from './types'
import { useEffect } from 'react'

function App() {
  const { setViewMode, viewMode, unreadCount } = useChatStore();

  const handleRestoreFromMinimized = () => {
    setViewMode(VIEW_MODE.CHATBOX);
  };



  // Apply CSS classes to body based on view mode
  useEffect(() => {
    const body = document.body;

    // Remove all chat-related classes
    body.classList.remove('chat-fullpage', 'chat-chatbox', 'chat-minimized');

    // Add the appropriate class based on current view mode
    switch (viewMode) {
      case VIEW_MODE.FULLPAGE:
        body.classList.add('chat-fullpage');
        break;
      case VIEW_MODE.CHATBOX:
        body.classList.add('chat-chatbox');
        break;
      case VIEW_MODE.MINIMIZED:
        body.classList.add('chat-minimized');
        break;
    }

    // Cleanup on unmount
    return () => {
      body.classList.remove('chat-fullpage', 'chat-chatbox', 'chat-minimized');
    };
  }, [viewMode]);

  return (
    <QueryProvider>
      <div className="app">
        {/* Eneco Homepage */}
        <Homepage />

        <ChatContainer />

        {/* Floating Chat Button - only visible when minimized */}
        <FloatingChatButton
          isVisible={viewMode === VIEW_MODE.MINIMIZED}
          onClick={handleRestoreFromMinimized}
          unreadCount={unreadCount}
        />

        {/* Streaming Test Panel - only visible in development */}
        {/* <StreamingTestPanel /> */}
      </div>
    </QueryProvider>
  )
}

export default App
