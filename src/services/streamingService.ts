import {
  type StreamingService,
  type StreamingChunk,
  type SendMessagePayload,
  type StreamingConfig,
  DEFAULT_API_CONFIG,
} from '../types/api';

// Default streaming configuration
const DEFAULT_STREAMING_CONFIG: StreamingConfig = {
  enabled: true,
  chunkSize: 1024,
  timeout: 30000,
  retryAttempts: 3,
  retryDelay: 1000,
};

// Implementation of StreamingService
class StreamingServiceImpl implements StreamingService {
  private config: StreamingConfig;

  constructor(config: Partial<StreamingConfig> = {}) {
    this.config = { ...DEFAULT_STREAMING_CONFIG, ...config };
  }

  async handleStreamingMessage(payload: SendMessagePayload): Promise<Response> {
    const endpoint = `${DEFAULT_API_CONFIG.baseURL}/chat`;

    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/plain',
          // Add authentication headers if available
          ...(import.meta.env.VITE_API_KEY && { 'X-API-Key': import.meta.env.VITE_API_KEY }),
          ...(import.meta.env.VITE_AUTH_TOKEN && { 'Authorization': `Bearer ${import.meta.env.VITE_AUTH_TOKEN}` }),
        },
        body: JSON.stringify({
          ...payload,
          stream: true, // Enable streaming
        }),
        signal: AbortSignal.timeout(this.config.timeout || 30000),
      });

      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unknown error');
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      if (!response.body) {
        throw new Error('No response body for streaming');
      }

      return response;
    } catch (error) {
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('Streaming request timed out');
        }
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
          throw new Error('Network error: Unable to connect to the server');
        }
      }
      throw error;
    }
  }

  async* processStreamingResponse(response: Response): AsyncGenerator<StreamingChunk, void, unknown> {
    if (!response.body) {
      throw new Error('No response body for streaming');
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let retryCount = 0;

    try {
      while (true) {
        try {
          const { value, done } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value, { stream: true });

          // Reset retry count on successful read
          retryCount = 0;

          // Process chunk and yield parsed streaming chunks
          yield* this.parseChunk(chunk);
        } catch (error) {
          retryCount++;

          if (retryCount >= (this.config.retryAttempts || 3)) {
            throw new Error(`Streaming failed after ${retryCount} attempts: ${error instanceof Error ? error.message : 'Unknown error'}`);
          }

          // Wait before retrying
          await new Promise(resolve => setTimeout(resolve, this.config.retryDelay || 1000));

          // Continue to next iteration to retry
          continue;
        }
      }
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Streaming was interrupted');
      }
      throw error;
    } finally {
      try {
        reader.releaseLock();
      } catch {
        // Ignore errors when releasing lock
      }
    }
  }

  private* parseChunk(chunk: string): Generator<StreamingChunk, void, unknown> {
    // Handle thread ID
    if (chunk.includes('[STARTED THREAD]:')) {
      const parts = chunk.split(':');
      if (parts.length > 1) {
        const threadId = parts[1].trim();
        yield {
          type: 'thread_id',
          content: threadId,
        };
      }
      return;
    }

    // Handle tool calls
    if (chunk.includes('[TOOLCALL]')) {
      const toolCallRegex = /\[TOOLCALL\]\s*([^[]+?)\s*\[ENDTOOLCALL\]/g;
      let match;
      
      while ((match = toolCallRegex.exec(chunk)) !== null) {
        const toolName = match[1].trim();
        if (toolName) {
          yield {
            type: 'tool_call',
            content: toolName,
          };
        }
      }
      return;
    }

    // Handle response start
    if (chunk.includes('[STARTED RESPONSE]:')) {
      const responseRegex = /\[STARTED RESPONSE\]:\s*(.*)/;
      const responseMatch = chunk.match(responseRegex);
      
      yield {
        type: 'response_start',
        content: '',
      };

      if (responseMatch && responseMatch[1]) {
        const firstContentChunk = responseMatch[1];
        if (firstContentChunk.trim()) {
          yield {
            type: 'text',
            content: firstContentChunk,
          };
        }
      }
      return;
    }

    // Handle images
    if (chunk.includes('[IMAGE]:')) {
      const imageStartIndex = chunk.indexOf('[IMAGE]:');
      const textBeforeImage = chunk.substring(0, imageStartIndex);
      const imageDataStart = chunk.substring(imageStartIndex + '[IMAGE]:'.length);

      // Yield text before image if any
      if (textBeforeImage.trim()) {
        yield {
          type: 'text',
          content: textBeforeImage,
        };
      }

      // Start image processing
      yield {
        type: 'image',
        content: imageDataStart,
        metadata: { imageStart: true },
      };
      return;
    }

    // Handle image end
    if (chunk.includes('[IMAGE_END]')) {
      const endIndex = chunk.indexOf('[IMAGE_END]');
      const imageData = chunk.substring(0, endIndex);
      const remainingText = chunk.substring(endIndex + '[IMAGE_END]'.length);

      // Complete image
      if (imageData.trim()) {
        yield {
          type: 'image',
          content: imageData.trim(),
          metadata: { imageEnd: true },
        };
      }

      // Yield remaining text if any
      if (remainingText.trim()) {
        yield {
          type: 'text',
          content: remainingText,
        };
      }
      return;
    }

    // Handle regular text chunks
    if (chunk.trim()) {
      yield {
        type: 'text',
        content: chunk,
      };
    }
  }

  // Update configuration
  updateConfig(config: Partial<StreamingConfig>): void {
    this.config = { ...this.config, ...config };
  }

  // Get current configuration
  getConfig(): StreamingConfig {
    return { ...this.config };
  }
}

// Export singleton instance
export const streamingService = new StreamingServiceImpl();

// Export class for custom instances
export { StreamingServiceImpl };

// Service factory
export const createStreamingService = (config?: Partial<StreamingConfig>): StreamingService => {
  return new StreamingServiceImpl(config);
};

// Default export
export default streamingService;
