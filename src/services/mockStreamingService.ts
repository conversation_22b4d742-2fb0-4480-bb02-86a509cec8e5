import type { SendMessagePayload } from '../types/api';

// Mock streaming responses with realistic delays and content
const MOCK_RESPONSES = [
  {
    toolCalls: ['orchestrator-agent'],
    response: "Hello! I'm <PERSON>, your Eneco assistant. I'm here to help you with questions about your energy usage, billing, and account management. How can I assist you today?",
    images: [],
  },
  {
    toolCalls: ['scenario-agent', 'installment-amount-agent'],
    response: "I can help you understand your current energy usage and billing. Based on your account, I can see you're on a variable rate plan. Would you like me to analyze your recent usage patterns or help you adjust your monthly installment amount?",
    images: [],
  },
  {
    toolCalls: ['visualisation-agent'],
    response: "Here's a visualization of your energy usage over the past 6 months. As you can see, your consumption has been relatively stable, with a slight increase during the winter months. This is completely normal for most households.",
    images: ['iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='], // Tiny 1x1 pixel image
  },
  {
    toolCalls: ['installment-amount-agent'],
    response: "Based on your current usage patterns and the latest energy prices, I recommend adjusting your monthly installment to €125. This should help you avoid a large settlement at the end of the year while keeping your monthly payments manageable.",
    images: [],
  },
  {
    toolCalls: ['orchestrator-agent', 'scenario-agent'],
    response: "I've analyzed several scenarios for your energy plan. Here are three options that might work better for you:\n\n1. **Fixed Rate Plan**: Provides price certainty for 12 months\n2. **Green Energy Plan**: 100% renewable energy with competitive rates\n3. **Smart Plan**: Dynamic pricing based on market rates\n\nWould you like me to explain any of these options in more detail?",
    images: [],
  },
];

class MockStreamingService {
  private getRandomResponse() {
    return MOCK_RESPONSES[Math.floor(Math.random() * MOCK_RESPONSES.length)];
  }

  private async delay(ms: number) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async createMockStreamingResponse(payload: SendMessagePayload): Promise<Response> {
    const mockResponse = this.getRandomResponse();
    const threadId = `thread_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    // Create a readable stream that simulates the streaming API response
    const stream = new ReadableStream({
      async start(controller) {
        try {
          // Build the initial chunk with thread ID and tool calls (like the example)
          let initialChunk = `[STARTED THREAD]: ${threadId}`;

          // Add tool calls to the same chunk
          for (const tool of mockResponse.toolCalls) {
            initialChunk += `[TOOLCALL] ${tool} [ENDTOOLCALL]`;
          }

          // Add response start to the same chunk
          initialChunk += `[STARTED RESPONSE]: `;

          // Send the initial chunk all at once
          controller.enqueue(new TextEncoder().encode(initialChunk));
          await new Promise(resolve => setTimeout(resolve, 300));

          // Stream the response text word by word
          const words = mockResponse.response.split(' ');
          for (let i = 0; i < words.length; i++) {
            const word = words[i];
            const chunk = i === 0 ? word : ` ${word}`;

            // Stream word by word with realistic delays
            controller.enqueue(new TextEncoder().encode(chunk));

            // Variable delay to simulate realistic typing
            const delay = 50 + Math.random() * 150;
            await new Promise(resolve => setTimeout(resolve, delay));
          }

          // 5. Send images if any
          for (const image of mockResponse.images) {
            await new Promise(resolve => setTimeout(resolve, 300));
            controller.enqueue(new TextEncoder().encode(`\n[IMAGE]:${image}[IMAGE_END]\n`));
          }

          // 6. Complete the stream
          controller.close();
        } catch (error) {
          controller.error(error);
        }
      }
    });

    // Return a mock Response object
    return new Response(stream, {
      status: 200,
      statusText: 'OK',
      headers: {
        'Content-Type': 'text/plain',
        'Transfer-Encoding': 'chunked',
      },
    });
  }

  // Method to simulate different error scenarios for testing
  async createMockErrorResponse(errorType: 'timeout' | 'network' | 'server' | 'interrupted'): Promise<Response> {
    switch (errorType) {
      case 'timeout':
        await this.delay(35000); // Longer than typical timeout
        throw new Error('Request timeout');
      
      case 'network':
        throw new TypeError('Failed to fetch');
      
      case 'server':
        return new Response('Internal Server Error', {
          status: 500,
          statusText: 'Internal Server Error',
        });
      
      case 'interrupted': {
        const stream = new ReadableStream({
          start(controller) {
            controller.enqueue(new TextEncoder().encode('[STARTED THREAD]: test_thread[TOOLCALL] orchestrator-agent [ENDTOOLCALL][STARTED RESPONSE]: This response will be inter'));
            // Simulate interruption
            controller.error(new Error('Stream interrupted'));
          }
        });
        return new Response(stream, { status: 200 });
      }
      
      default:
        throw new Error('Unknown error type');
    }
  }
}

// Export singleton instance
export const mockStreamingService = new MockStreamingService();

// Export class for custom instances
export { MockStreamingService };

// Default export
export default mockStreamingService;
