import React from 'react';
import clsx from 'clsx';
import edwinImage from '../../assets/edwin.png';

interface FloatingChatButtonProps {
  onClick: () => void;
  isVisible: boolean;
  unreadCount?: number;
  className?: string;
  children?: React.ReactNode;
}

const FloatingChatButton: React.FC<FloatingChatButtonProps> = ({
  onClick,
  isVisible,
  unreadCount = 0,
  className,
  children,
}) => {
  if (!isVisible) {
    return null;
  }



  return (
    <button
      onClick={onClick}
      className={clsx('floating-chat-button', className)}
      aria-label="Open Eneco Assistant"
    >
      <div className="floating-chat-button__avatar">
        <img 
          src={edwinImage} 
          alt="Edwin - Eneco Assistant" 
          className="floating-chat-button__image"
        />
      </div>
      
      {/* Unread message badge */}
      {unreadCount > 0 && (
        <div className="floating-chat-button__badge">
          {unreadCount > 99 ? '99+' : unreadCount}
        </div>
      )}

      {/* Optional pulse indicator for new messages */}
      <div className={clsx(
        'floating-chat-button__pulse',
        { 'floating-chat-button__pulse--active': unreadCount > 0 }
      )} />

      {children}
    </button>
  );
};

export default FloatingChatButton;
