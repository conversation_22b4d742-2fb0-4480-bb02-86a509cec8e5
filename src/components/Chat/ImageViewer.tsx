import React, { useEffect, useCallback } from 'react';
import { X, ChevronLeft, ChevronRight } from 'lucide-react';
import clsx from 'clsx';

interface ImageViewerProps {
  images: string[];
  currentIndex: number;
  isOpen: boolean;
  onClose: () => void;
  onNavigate?: (index: number) => void;
  className?: string;
}

const ImageViewer: React.FC<ImageViewerProps> = ({
  images,
  currentIndex,
  isOpen,
  onClose,
  onNavigate,
  className,
}) => {
  const handlePrevious = useCallback(() => {
    if (images.length > 1) {
      const newIndex = currentIndex > 0 ? currentIndex - 1 : images.length - 1;
      onNavigate?.(newIndex);
    }
  }, [currentIndex, images.length, onNavigate]);

  const handleNext = useCallback(() => {
    if (images.length > 1) {
      const newIndex = currentIndex < images.length - 1 ? currentIndex + 1 : 0;
      onNavigate?.(newIndex);
    }
  }, [currentIndex, images.length, onNavigate]);

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!isOpen) return;

    switch (event.key) {
      case 'Escape':
        onClose();
        break;
      case 'ArrowLeft':
        handlePrevious();
        break;
      case 'ArrowRight':
        handleNext();
        break;
    }
  }, [isOpen, onClose, handlePrevious, handleNext]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  if (!isOpen || !images || images.length === 0 || currentIndex < 0 || currentIndex >= images.length) {
    return null;
  }

  const currentImage = images[currentIndex];
  const hasMultipleImages = images.length > 1;

  const handleBackdropClick = (event: React.MouseEvent) => {
    if (event.target === event.currentTarget) {
      onClose();
    }
  };

  const handleImageError = (_event: React.SyntheticEvent<HTMLImageElement>) => {
    // Handle image loading error silently
  };

  const getImageDataUrl = (imageData: string) => {
    // Try to detect image format from base64 data
    if (imageData.startsWith('iVBORw0KGgo')) {
      return `data:image/png;base64,${imageData}`;
    } else if (imageData.startsWith('/9j/')) {
      return `data:image/jpeg;base64,${imageData}`;
    } else {
      // Default to PNG for unknown formats
      return `data:image/png;base64,${imageData}`;
    }
  };

  return (
    <div
      className={clsx('image-viewer', className)}
      onClick={handleBackdropClick}
    >
      <div className="image-viewer__backdrop" />
      
      {/* Close button */}
      <button
        className="image-viewer__close"
        onClick={onClose}
        aria-label="Close image viewer"
      >
        <X size={24} />
      </button>

      {/* Navigation buttons */}
      {hasMultipleImages && (
        <>
          <button
            className="image-viewer__nav image-viewer__nav--prev"
            onClick={handlePrevious}
            aria-label="Previous image"
          >
            <ChevronLeft size={32} />
          </button>
          
          <button
            className="image-viewer__nav image-viewer__nav--next"
            onClick={handleNext}
            aria-label="Next image"
          >
            <ChevronRight size={32} />
          </button>
        </>
      )}

      {/* Image container */}
      <div className="image-viewer__container">
        <img
          src={getImageDataUrl(currentImage)}
          alt={`Image ${currentIndex + 1} of ${images.length}`}
          className="image-viewer__image"
          onError={handleImageError}
        />
      </div>

      {/* Image counter */}
      {hasMultipleImages && (
        <div className="image-viewer__counter">
          {currentIndex + 1} / {images.length}
        </div>
      )}
    </div>
  );
};

export default ImageViewer;
