import React, { useState, useMemo } from 'react';
import { formatDistanceToNow } from 'date-fns';
import clsx from 'clsx';
import ImageGallery from './ImageGallery';
import ImageViewer from './ImageViewer';
import MarkdownRenderer from './MarkdownRenderer';

// Simplified message interface
interface SimpleMessage {
  id: string;
  content: string;
  sender: 'user' | 'agent';
  timestamp: Date;
  images?: string[]; // Optional array of base64-encoded images
  isStreaming?: boolean; // Whether this message is currently being streamed
  isComplete?: boolean; // Whether streaming is complete for this message
  toolInfo?: string; // Information about tool calls during streaming
}

interface MessageBubbleProps {
  message: SimpleMessage;
  isOwn: boolean;
  showTimestamp?: boolean;
  className?: string;
  children?: React.ReactNode;
}

// Function to process content and extract embedded images
const processMessageContent = (content: string, existingImages: string[] = []) => {
  // Look for embedded base64 images in content (fallback for any missed during streaming)
  const imageRegex = /\[IMAGE\]:\s*([A-Za-z0-9+/=\s]+?)\s*\[IMAGE_END\]/g;
  let processedContent = content;
  const extractedImages: string[] = [];

  let match;
  while ((match = imageRegex.exec(content)) !== null) {
    const imageData = match[1].replace(/\s+/g, ''); // Clean whitespace
    if (imageData.length > 100) { // Basic validation
      extractedImages.push(imageData);
    }
    // Remove the image tag from content
    processedContent = processedContent.replace(match[0], '');
  }

  // Combine existing images with any newly extracted ones
  const allImages = [...existingImages, ...extractedImages];

  return {
    content: processedContent.trim(),
    images: allImages.length > 0 ? allImages : undefined,
  };
};

const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isOwn,
  showTimestamp = true,
  className,
  children,
}) => {
  const [isImageViewerOpen, setIsImageViewerOpen] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Process message content to extract any embedded images
  const processedMessage = useMemo(() => {
    const processed = processMessageContent(message.content, message.images);
    return {
      ...message,
      content: processed.content,
      images: processed.images,
    };
  }, [message.content, message.images]);

  const formatTimestamp = (timestamp: Date) => {
    try {
      return formatDistanceToNow(timestamp, { addSuffix: true });
    } catch {
      return timestamp.toLocaleTimeString();
    }
  };

  const handleImageClick = (imageIndex: number) => {
    setCurrentImageIndex(imageIndex);
    setIsImageViewerOpen(true);
  };

  const handleCloseImageViewer = () => {
    setIsImageViewerOpen(false);
  };

  const handleNavigateImage = (index: number) => {
    setCurrentImageIndex(index);
  };

  return (
    <>
      <div
        className={clsx(
          'message-bubble',
          {
            'message-bubble--own': isOwn,
            'message-bubble--other': !isOwn,
            'message-bubble--has-images': processedMessage.images && processedMessage.images.length > 0,
          },
          className
        )}
      >
        <div className="message-bubble__content">
          {/* Show tool info for streaming messages */}
          {message.toolInfo && message.isStreaming && (
            <div className="message-bubble__tool-info">
              <span className="message-bubble__tool-icon">⚡</span>
              {message.toolInfo}
            </div>
          )}

          <div className={clsx('message-bubble__text', {
            'message-bubble__text--streaming': message.isStreaming
          })}>
            {/* Render agent messages with markdown support */}
            {processedMessage.sender === 'agent' ? (
              <MarkdownRenderer
                content={processedMessage.content}
                isStreaming={processedMessage.isStreaming}
              />
            ) : (
              processedMessage.content
            )}
            {/* Show streaming cursor */}
            {processedMessage.isStreaming && !processedMessage.isComplete && (
              <span className="message-bubble__streaming-cursor">|</span>
            )}
          </div>

          {/* Render images if present */}
          {processedMessage.images && processedMessage.images.length > 0 && (
            <div className="message-bubble__images">
              <ImageGallery
                images={processedMessage.images}
                onImageClick={handleImageClick}
              />
            </div>
          )}


        </div>
        {showTimestamp && (
            <div className="message-bubble__timestamp">
              {formatTimestamp(message.timestamp)}
            </div>
        )}

        {children}
      </div>

      {/* Image Viewer Modal */}
      {processedMessage.images && processedMessage.images.length > 0 && (
        <ImageViewer
          images={processedMessage.images}
          currentIndex={currentImageIndex}
          isOpen={isImageViewerOpen}
          onClose={handleCloseImageViewer}
          onNavigate={handleNavigateImage}
        />
      )}
    </>
  );
};

export default MessageBubble;
