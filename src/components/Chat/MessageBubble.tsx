import React, { useState } from 'react';
import { formatDistanceToNow } from 'date-fns';
import clsx from 'clsx';
import ImageGallery from './ImageGallery';
import ImageViewer from './ImageViewer';
import MarkdownRenderer from './MarkdownRenderer';

// Simplified message interface
interface SimpleMessage {
  id: string;
  content: string;
  sender: 'user' | 'agent';
  timestamp: Date;
  images?: string[]; // Optional array of base64-encoded images
  isStreaming?: boolean; // Whether this message is currently being streamed
  isComplete?: boolean; // Whether streaming is complete for this message
  toolInfo?: string; // Information about tool calls during streaming
}

interface MessageBubbleProps {
  message: SimpleMessage;
  isOwn: boolean;
  showTimestamp?: boolean;
  className?: string;
  children?: React.ReactNode;
}

const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isOwn,
  showTimestamp = true,
  className,
  children,
}) => {
  const [isImageViewerOpen, setIsImageViewerOpen] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const formatTimestamp = (timestamp: Date) => {
    try {
      return formatDistanceToNow(timestamp, { addSuffix: true });
    } catch {
      return timestamp.toLocaleTimeString();
    }
  };

  const handleImageClick = (imageIndex: number) => {
    setCurrentImageIndex(imageIndex);
    setIsImageViewerOpen(true);
  };

  const handleCloseImageViewer = () => {
    setIsImageViewerOpen(false);
  };

  const handleNavigateImage = (index: number) => {
    setCurrentImageIndex(index);
  };

  return (
    <>
      <div
        className={clsx(
          'message-bubble',
          {
            'message-bubble--own': isOwn,
            'message-bubble--other': !isOwn,
            'message-bubble--has-images': message.images && message.images.length > 0,
          },
          className
        )}
      >
        <div className="message-bubble__content">
          {/* Show tool info for streaming messages */}
          {message.toolInfo && message.isStreaming && (
            <div className="message-bubble__tool-info">
              <span className="message-bubble__tool-icon">⚡</span>
              {message.toolInfo}
            </div>
          )}

          <div className={clsx('message-bubble__text', {
            'message-bubble__text--streaming': message.isStreaming
          })}>
            {/* Render agent messages with markdown support */}
            {message.sender === 'agent' ? (
              <MarkdownRenderer
                content={message.content}
                isStreaming={message.isStreaming}
              />
            ) : (
              message.content
            )}
            {/* Show streaming cursor */}
            {message.isStreaming && !message.isComplete && (
              <span className="message-bubble__streaming-cursor">|</span>
            )}
          </div>

          {/* Render images if present */}
          {message.images && message.images.length > 0 && (
            <div className="message-bubble__images">
              <ImageGallery
                images={message.images}
                onImageClick={handleImageClick}
              />
            </div>
          )}


        </div>
        {showTimestamp && (
            <div className="message-bubble__timestamp">
              {formatTimestamp(message.timestamp)}
            </div>
        )}

        {children}
      </div>

      {/* Image Viewer Modal */}
      {message.images && message.images.length > 0 && (
        <ImageViewer
          images={message.images}
          currentIndex={currentImageIndex}
          isOpen={isImageViewerOpen}
          onClose={handleCloseImageViewer}
          onNavigate={handleNavigateImage}
        />
      )}
    </>
  );
};

export default MessageBubble;
