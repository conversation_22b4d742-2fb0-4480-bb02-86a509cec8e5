import React from 'react';
import clsx from 'clsx';
import edwinImage from '../../assets/edwin.png';

interface TypingIndicatorProps {
  className?: string;
  children?: React.ReactNode;
}

const TypingIndicator: React.FC<TypingIndicatorProps> = ({
  className,
  children,
}) => {
  return (
    <div className={clsx('typing-indicator', className)}>
      <div className="typing-indicator__content">
        <div className="typing-indicator__avatar">
          <img
            src={edwinImage}
            alt="Edwin is typing..."
            className="typing-indicator__avatar-image"
          />
        </div>
        <div className="typing-indicator__bubble">
          <div className="typing-indicator__dots">
            <span className="typing-indicator__dot"></span>
            <span className="typing-indicator__dot"></span>
            <span className="typing-indicator__dot"></span>
          </div>
          <div className="typing-indicator__text">AI is typing...</div>
        </div>
      </div>
      {children}
    </div>
  );
};

export default TypingIndicator;
