import React from 'react';
import clsx from 'clsx';
import edwinImage from '../../assets/edwin.png';
import { useChatStore } from '../../store/chatStore';
import { getAgentTypingMessage, DEFAULT_TYPING_MESSAGE } from '../../constants/agents';

interface TypingIndicatorProps {
  className?: string;
  children?: React.ReactNode;
}

const TypingIndicator: React.FC<TypingIndicatorProps> = ({
  className,
  children,
}) => {
  const currentAgent = useChatStore((state) => state.currentAgent);

  // Get agent-specific typing message
  const typingMessage = currentAgent
    ? getAgentTypingMessage(currentAgent)
    : DEFAULT_TYPING_MESSAGE;

  return (
    <div className={clsx('typing-indicator', className)}>
      <div className="typing-indicator__content">
        <div className="typing-indicator__avatar">
          <img
            src={edwinImage}
            alt="<PERSON> is typing..."
            className="typing-indicator__avatar-image"
          />
        </div>
        <div className="typing-indicator__bubble">
          <div className="typing-indicator__dots">
            <span className="typing-indicator__dot"></span>
            <span className="typing-indicator__dot"></span>
            <span className="typing-indicator__dot"></span>
          </div>
          <div className="typing-indicator__text">{typingMessage}</div>
        </div>
      </div>
      {children}
    </div>
  );
};

export default TypingIndicator;
