This file is a merged representation of a subset of the codebase, containing specifically included files, combined into a single document by <PERSON>omix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Only files matching these patterns are included: **/*.ts, **/*.tsx, **/*.json
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

</file_summary>

<directory_structure>
components/
  ChatHeader.tsx
  ChatInput.tsx
  ChatMessage.tsx
  ChatMessages.tsx
  index.ts
  InfoSection.tsx
  ScenarioCards.tsx
  TopBar.tsx
hooks/
  useChat.ts
  useDarkMode.ts
  useStreaming.ts
services/
  ChatApiService.ts
types/
  index.ts
App.tsx
index.tsx
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="components/ChatHeader.tsx">
import React from 'react';
import {
    Button,
    makeStyles,
    tokens,
} from '@fluentui/react-components';
import {
    Add24Regular,
} from '@fluentui/react-icons';

const useStyles = makeStyles({
    topbar: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: `${tokens.spacingVerticalM} ${tokens.spacingHorizontalL}`,
        backgroundColor: tokens.colorBrandBackground,
        height: '40px',
    },
    text: {
        fontSize: tokens.fontSizeBase400,
        fontWeight: tokens.fontWeightSemibold,
        color: tokens.colorNeutralForegroundOnBrand,
    },
    actions: {
        display: 'flex',
        alignItems: 'center',
        gap: tokens.spacingHorizontalS,
    },
    userMenu: {
        padding: tokens.spacingVerticalM,
        minWidth: '200px',
    },
    userMenuLabel: {
        marginBottom: tokens.spacingVerticalXS,
    },
});

interface ChatHeaderProps {
    onNewChat: () => void;
}

export const ChatHeader: React.FC<ChatHeaderProps> = ({
    onNewChat,
}) => {
    const styles = useStyles();

    return (
        <div className={styles.topbar}>
            <div className={styles.text}>Chat</div>
            <div className={styles.actions}>
                <Button
                    appearance="subtle"
                    icon={<Add24Regular />}
                    onClick={onNewChat}
                    className={styles.text}
                    title="Start new chat"
                />      
            </div>
        </div>
    );
};
</file>

<file path="components/ChatInput.tsx">
import React, { useState } from 'react';
import {
    Button,
    Input,
    makeStyles,
    tokens,
} from '@fluentui/react-components';
import { Send24Regular } from '@fluentui/react-icons';

const useStyles = makeStyles({
    inputContainer: {
        padding: tokens.spacingVerticalM,
        backgroundColor: tokens.colorNeutralBackground1,
    },
    input: {
        width: '100%',
    },
    sendButton: {
        minWidth: 'auto',
        padding: '4px',
    },
});

interface ChatInputProps {
    loading: boolean;
    onSendMessage: (message: string) => void;
}

export const ChatInput: React.FC<ChatInputProps> = ({ loading, onSendMessage }) => {
    const [input, setInput] = useState('');
    const styles = useStyles();

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (!input.trim() || loading) return;

        onSendMessage(input);
        setInput('');
    };

    const handleKeyPress = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSubmit(e);
        }
    };

    return (
        <form className={styles.inputContainer} onSubmit={handleSubmit}>
            <Input
                className={styles.input}
                value={input}
                appearance='filled-darker'
                size="large"
                onChange={(_, data) => setInput(data.value)}
                onKeyDown={handleKeyPress}
                placeholder="Typ je bericht..."
                disabled={loading}
                contentAfter={
                    <Button
                        appearance="transparent"
                        icon={<Send24Regular />}
                        className={styles.sendButton}
                        onClick={handleSubmit}
                        disabled={loading || !input.trim()}
                        aria-label={loading ? 'Versturen...' : 'Verstuur bericht'}
                        size="small"
                    />
                }
            />
        </form>
    );
};
</file>

<file path="components/ChatMessage.tsx">
import React from 'react';
import {
    makeStyles,
    tokens,
    ProgressBar,
} from '@fluentui/react-components';
import ReactMarkdown from 'react-markdown';
import { ChatMessage as ChatMessageType } from '../types';

const useStyles = makeStyles({
    message: {
        marginBottom: tokens.spacingVerticalM,
        display: 'flex',
        flexDirection: 'column',
    },
    userMessage: {
        alignSelf: 'flex-end',
        maxWidth: '70%',
    },
    botMessage: {
        alignSelf: 'flex-start',
        maxWidth: '70%',
    },
    messageContent: {
        padding: tokens.spacingVerticalM,
        borderRadius: tokens.borderRadiusMedium,
        wordWrap: 'break-word',
    },
    userContent: {
        backgroundColor: tokens.colorBrandBackground,
        color: tokens.colorNeutralForegroundOnBrand,
    },
    botContent: {
        backgroundColor: tokens.colorNeutralBackground3,
        color: tokens.colorNeutralForeground1,
    },
    typingIndicator: {
        display: 'flex',
        flexDirection: 'column',
        gap: tokens.spacingVerticalXS,
        padding: tokens.spacingVerticalM,
        backgroundColor: tokens.colorNeutralBackground3,
        borderRadius: tokens.borderRadiusMedium,
        minWidth: '120px',
    },
    toolCalls: {
        display: 'flex',
        flexDirection: 'column',
        gap: tokens.spacingVerticalXXS,
        marginBottom: tokens.spacingVerticalXS,
    },
    toolCall: {
        fontSize: tokens.fontSizeBase200,
        color: tokens.colorNeutralForeground2,
        fontStyle: 'italic',
    },
    progressContainer: {
        display: 'flex',
        alignItems: 'center',
        gap: tokens.spacingHorizontalS,
    },
    images: {
        display: 'flex',
        flexDirection: 'column',
        gap: tokens.spacingVerticalS,
        marginTop: tokens.spacingVerticalS,
    },
    image: {
        width: '100%',
        height: 'auto',
        borderRadius: tokens.borderRadiusSmall,
        objectFit: 'cover',
        maxHeight: '400px', // Prevent extremely tall images
    },
    markdown: {
        '& p': {
            margin: 0,
            marginBottom: tokens.spacingVerticalXS,
        },
        '& p:last-child': {
            marginBottom: 0,
        },
        '& ul, & ol': {
            marginTop: tokens.spacingVerticalXS,
            marginBottom: tokens.spacingVerticalXS,
            paddingLeft: tokens.spacingHorizontalL,
        },
        '& li': {
            marginBottom: tokens.spacingVerticalXXS,
        },
        '& code': {
            backgroundColor: tokens.colorNeutralBackground4,
            padding: `${tokens.spacingVerticalXXS} ${tokens.spacingHorizontalXS}`,
            borderRadius: tokens.borderRadiusSmall,
            fontFamily: tokens.fontFamilyMonospace,
        },
        '& pre': {
            backgroundColor: tokens.colorNeutralBackground4,
            padding: tokens.spacingVerticalS,
            borderRadius: tokens.borderRadiusSmall,
            overflow: 'auto',
            '& code': {
                backgroundColor: 'transparent',
                padding: 0,
            },
        },
    },
});

interface ChatMessageProps {
    message: ChatMessageType;
    streaming: boolean;
}

export const ChatMessage: React.FC<ChatMessageProps> = ({ message, streaming }) => {
    const styles = useStyles();

    // Loader including tool call info in case of streaming.
    if (message.isTyping) {
        return (
            <div className={`${styles.message} ${styles.botMessage}`}>
                <div className={styles.typingIndicator}>
                    {(streaming && message.typingInfo?.trim() != '') && (
                        <div className={styles.toolCalls}>
                            <div className={styles.toolCall}>
                                {message.typingInfo}
                            </div>
                        </div>
                    )}
                    <div className={styles.progressContainer}>
                        <ProgressBar />
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className={`${styles.message} ${message.sender === 'user' ? styles.userMessage : styles.botMessage}`}>
            <div className={`${styles.messageContent} ${message.sender === 'user' ? styles.userContent : styles.botContent}`}>
                {message.sender === 'bot' ? (
                    <div className={styles.markdown}>
                        <ReactMarkdown>{message.text}</ReactMarkdown>
                    </div>
                ) : (
                    message.text
                )}
                {message.images && message.images.length > 0 && (
                    <div className={styles.images}>
                        {message.images.map((image, index) => (
                            <img
                                key={index}
                                src={`data:image/png;base64,${image}`}
                                alt={`Response ${index + 1}`}
                                className={styles.image}
                                onError={(e) => {
                                    console.error('Failed to load image:', e);
                                    (e.target as HTMLImageElement).style.display = 'none';
                                }}
                            />
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
};
</file>

<file path="components/ChatMessages.tsx">
import React, { useEffect, useRef } from 'react';
import {
    makeStyles,
    tokens,
} from '@fluentui/react-components';
import { ChatMessage } from './ChatMessage';
import { ChatMessage as ChatMessageType } from '../types';

const useStyles = makeStyles({
    container: {
        flex: 1,
        overflowY: 'auto',
        padding: tokens.spacingVerticalM,
        display: 'flex',
        flexDirection: 'column',
        gap: tokens.spacingVerticalS,
    },
});

interface ChatMessagesProps {
    messages: ChatMessageType[];
    streaming: boolean;
}

export const ChatMessages: React.FC<ChatMessagesProps> = ({ messages, streaming }) => {
    const styles = useStyles();
    const chatEndRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (chatEndRef.current) {
            const container = chatEndRef.current.parentElement;
            if (container) {
                const isScrolledToBottom =
                    container.scrollHeight - container.clientHeight <= container.scrollTop + 1;

                // Only auto-scroll if user is already at or near the bottom
                if (isScrolledToBottom || messages.length <= 2) {
                    chatEndRef.current.scrollIntoView({ behavior: 'smooth' });
                }
            }
        }
    }, [messages]);

    return (
        <div className={styles.container}>
            {messages.map((message, index) => (
                <ChatMessage key={index} message={message} streaming={streaming} />
            ))}
            <div ref={chatEndRef} />
        </div>
    );
};
</file>

<file path="components/index.ts">
export { TopBar } from './TopBar';
export { ChatMessage } from './ChatMessage';
export { ChatMessages } from './ChatMessages';
export { ChatInput } from './ChatInput';
export { ScenarioCards } from './ScenarioCards';
</file>

<file path="components/InfoSection.tsx">
import React from 'react';
import {
    makeStyles,
    tokens,
    Accordion,
    AccordionItem,
    AccordionHeader,
    AccordionPanel
} from '@fluentui/react-components';


const AGENTS = [
    {
        id: 'orchestrator-agent',
        name: '🎯 Orchestrator Agent',
        description: 'Analyzes the user question and calls the right set of agents to answer the question properly.'
    },
    {
        id: 'visualization-agent',
        name: '📊 Visualization Agent',
        description: 'Generates graphs or charts to support the conversation and create clarity on installment amounts.'
    },
    {
        id: 'scenario-agent',
        name: '📅 Scenario Agent',
        description: 'Uses the MCP server to provide the user with next steps or recommendations based on the user\'s data.'
    },
    {
        id: 'rule-evaluation-agent',
        name: '📏 Rule Evaluation Agent',
        description: 'Evaluates the rules and policies that apply to the user\'s situation and provides guidance accordingly.'
    },
    {
        id: 'installment-agent',
        name: '💰 Update Installment Agent',
        description: 'Updates the installment amount for the user, taking into account the rules and policies defined by the company.'
    }
]
const useStyles = makeStyles({
    infoSection: {
        flexDirection: 'column',
        display: 'none',
        flex: 1,
        padding: '24px',
        backgroundColor: tokens.colorNeutralBackground3,
        borderRight: `1px solid ${tokens.colorNeutralStroke2}`,
        overflow: 'auto',
        width: '100%',
        // Tablet: show but still full width
        '@media (min-width: 768px)': {
            display: 'flex',
        },
        '@media (min-width: 1024px)': {
            display: 'flex',
            width: '350px',
            minWidth: '350px',
        },
    },
    infoTitle: {
        fontSize: tokens.fontSizeBase600,
        fontWeight: tokens.fontWeightSemibold,
        marginBottom: '16px',
        color: tokens.colorNeutralForeground1,
    },
    infoContent: {
        fontSize: tokens.fontSizeBase300,
        lineHeight: '1.5',
        color: tokens.colorNeutralForeground2,
        marginBottom: '24px',
    },
    accordion: {
        marginTop: '16px',
    },
    accordionPanel: {
        fontSize: tokens.fontSizeBase300,
        lineHeight: '1.4',
        color: tokens.colorNeutralForeground2,
    },
    accordionHeader: {
        fontWeight: tokens.fontWeightSemibold,
    },
});

export const InfoSection: React.FC = () => {
    const styles = useStyles();

    return (
        <div className={styles.infoSection}>
            <div className={styles.infoContent}>
                <p>
                    This sample demonstrates a multi-agent system that handles questions related to installment amounts. Some more details on the agents below:
                </p>
            </div>

            <Accordion className={styles.accordion} multiple collapsible defaultOpenItems={AGENTS.map(agent => agent.id)}>
                {AGENTS.map(agent => (
                    <AccordionItem key={agent.id} value={agent.id}>
                        <AccordionHeader className={styles.accordionHeader}>
                            {agent.name}
                        </AccordionHeader>
                        <AccordionPanel className={styles.accordionPanel}>
                            <p>{agent.description}</p>
                        </AccordionPanel>
                    </AccordionItem>
                ))}
            </Accordion>
        </div>
    );
};
</file>

<file path="components/ScenarioCards.tsx">
import React from 'react';
import {
    Button,
    makeStyles,
    tokens,
} from '@fluentui/react-components';
import { ScenarioCard as ScenarioCardType } from '../types';

const useStyles = makeStyles({
    container: {
        display: 'flex',
        gap: tokens.spacingHorizontalL,
        margin: `${tokens.spacingVerticalL} ${tokens.spacingHorizontalM}`,
        flexWrap: 'wrap',
    },
    card: {
        height: 'auto',
        flex: 1,
        padding: tokens.spacingVerticalL,
        textAlign: 'left',
        whiteSpace: 'pre-line',
        border: `1px solid ${tokens.colorBrandBackground2}`,
        borderRadius: tokens.borderRadiusMedium,
        transition: 'all 0.2s ease',
        ':hover': {
            border: `2px solid ${tokens.colorBrandBackground2Hover}`,
            transform: 'translateY(-1px)',
            boxShadow: tokens.shadow8,
        },
        ':active': {
            transform: 'translateY(0)',
        },
    },
});

interface ScenarioCardsProps {
    scenarios: ScenarioCardType[];
    onScenarioClick: (scenarioText: string) => void;
    loading: boolean;
}

export const ScenarioCards: React.FC<ScenarioCardsProps> = ({
    scenarios,
    onScenarioClick,
    loading
}) => {
    const styles = useStyles();

    return (
        <div className={styles.container}>
            {scenarios.map((scenario, index) => (
                <Button
                    key={index}
                    className={styles.card}
                    appearance="subtle"
                    onClick={() => onScenarioClick(scenario.text)}
                    disabled={loading}
                >
                    {scenario.text}
                </Button>
            ))}
        </div>
    );
};
</file>

<file path="components/TopBar.tsx">
import React from 'react';
import {
    Button,
    Input,
    makeStyles,
    Popover,
    PopoverSurface,
    PopoverTrigger,
    Switch,
    tokens
} from '@fluentui/react-components';
import {
    ArrowDownLeft24Regular,
    Person24Regular,
    SendClock24Regular,
    Stream24Regular,
    StreamInput20Regular,
    WeatherMoon24Regular,
    WeatherSunny24Regular,
} from '@fluentui/react-icons';

const useStyles = makeStyles({
    topbar: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: `${tokens.spacingVerticalM} ${tokens.spacingHorizontalL}`,
        backgroundColor: tokens.colorBrandBackground,
        height: '40px',
    },
    text: {
        fontSize: tokens.fontSizeBase400,
        fontWeight: tokens.fontWeightSemibold,
        color: tokens.colorNeutralForegroundOnBrand,
    },
    actions: {
        display: 'flex',
        alignItems: 'center',
        gap: tokens.spacingHorizontalS,
    },
    userMenu: {
        padding: tokens.spacingVerticalM,
        minWidth: '200px',
    },
    userMenuLabel: {
        marginBottom: tokens.spacingVerticalXS,
    },
});

interface TopBarProps {
    darkMode: boolean;
    onToggleDarkMode: () => void;
    onUserIdChange: (userId: string) => void;
    userId: string;
    streaming: boolean;
    onToggleStreaming: () => void;
}

export const TopBar: React.FC<TopBarProps> = ({
    darkMode,
    onToggleDarkMode,
    onUserIdChange,
    userId,
    streaming,
    onToggleStreaming,
}) => {
    const styles = useStyles();

    return (
        <div className={styles.topbar}>
            <div className={styles.text}>Installment Advisor Sample App</div>
            <div className={styles.actions}>
                <Button
                    appearance="subtle"
                    icon={streaming ? <Stream24Regular /> : <SendClock24Regular />}
                    onClick={onToggleStreaming}
                    className={styles.text}
                    title={streaming ? 'Streaming' : 'No streaming'}
                />
                <Button
                    appearance="subtle"
                    icon={darkMode ? <WeatherMoon24Regular /> : <WeatherSunny24Regular />}
                    onClick={onToggleDarkMode}
                    className={styles.text}
                    title={darkMode ? 'Light mode' : 'Dark mode'}
                />
                <Popover withArrow>
                    <PopoverTrigger disableButtonEnhancement>
                        <Button
                            appearance="subtle"
                            icon={<Person24Regular />}
                            className={styles.text}
                        >
                            {userId}
                        </Button>
                    </PopoverTrigger>
                    <PopoverSurface>
                        <div className={styles.userMenu}>
                            <Input
                                value={userId}
                                onChange={(_, data) => onUserIdChange(data.value)}
                                autoFocus
                            />
                        </div>
                    </PopoverSurface>
                </Popover>
            </div>
        </div>
    );
};
</file>

<file path="hooks/useChat.ts">
import { useState, useCallback } from 'react';
import { ChatMessage, ScenarioCard, ChatApiResponse } from '../types';
import { ChatApiService } from '../services/ChatApiService';
import { useStreaming } from './useStreaming';

const INITIAL_MESSAGE: ChatMessage = {
  sender: 'bot',
  text: 'Welkom bij de Installment Advisor! Stel hier je vraag over je termijnbedrag.'
};

const SCENARIO_CARDS: ScenarioCard[] = [
  {
    text: 'Ik wil mijn termijnbedrag graag zo aanpassen dat ik aan het eind niet hoef bij te betalen.'
  },
  {
    text: 'Welke energieprijzen betaal ik op dit moment'
  },
  {
    text: 'Ik kreeg vorig jaar geld terug, maar toch stijgt mijn termijnbedrag. Waarom?'
  }
];

export const useChat = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([INITIAL_MESSAGE]);
  const [loading, setLoading] = useState(false);
  const [threadId, setThreadId] = useState<string | null>(null);
  const [userId, setUserId] = useState('23456');
  const [streaming, setStreaming] = useState(false);

  // Initialize streaming hook
  const { processStreamingResponse } = useStreaming({
    setMessages,
    setThreadId
  });

  const sendMessage = useCallback(async (messageText: string) => {
    if (!messageText.trim() || loading) return;

    // Add user message to chat
    const userMessage: ChatMessage = { sender: 'user', text: messageText };
    setMessages(prev => [...prev, userMessage]);
    setLoading(true);

    // Add typing indicator
    const typingMessage: ChatMessage = { sender: 'bot', text: '', isTyping: true };
    setMessages(prev => [...prev, typingMessage]);

    try {
      const response = await ChatApiService.sendMessage({
        UserID: userId,
        message: messageText,
        stream: streaming,
        ...(threadId ? { threadId } : {})
      });

      if (streaming && response instanceof Response) {
        // Handle streaming response using the dedicated hook
        await processStreamingResponse(response);
      } else {
        // Handle non-streaming response
        const data = response as ChatApiResponse;

        if (data.threadId) {
          setThreadId(data.threadId);
        }

        // Remove typing indicator and add actual response
        setMessages(prev => [
          ...prev.slice(0, -1), // Remove typing indicator
          {
            sender: 'bot',
            text: data.reply || data.message || 'Er is een fout opgetreden. Probeer het later opnieuw.',
            isTyping: false,
            images: data.images || [],
          }
        ]);
      }
    } catch (error) {
      console.error('Chat error:', error);
      // Remove typing indicator and add error message
      setMessages(prev => [
        ...prev.slice(0, -1), // Remove typing indicator
        {
          sender: 'bot',
          text: 'Er is een fout opgetreden. Probeer het later opnieuw.',
          isTyping: false
        }
      ]);
    } finally {
      setLoading(false);
    }
  }, [loading, threadId, userId, streaming, processStreamingResponse]);

  const startNewChat = useCallback(() => {
    setMessages([INITIAL_MESSAGE]);
    setThreadId(null);
    setLoading(false);
  }, []);

  const resetUserId = (userId: string) => {
    setUserId(userId);
    startNewChat();
  };

  const toggleStreaming = useCallback(() => {
    setStreaming(prev => !prev);
  }, []);

  const hasUserChatted = messages.some(m => m.sender === 'user');

  return {
    messages,
    loading,
    userId,
    streaming,
    scenarioCards: SCENARIO_CARDS,
    hasUserChatted,
    sendMessage,
    startNewChat,
    resetUserId,
    toggleStreaming,
  };
};
</file>

<file path="hooks/useDarkMode.ts">
import { useState, useEffect } from 'react';

export const useDarkMode = () => {
  const [darkMode, setDarkMode] = useState(false);

  useEffect(() => {
    // Load dark mode preference from localStorage
    const savedDarkMode = localStorage.getItem('darkMode');
    if (savedDarkMode) {
      const isDarkMode = JSON.parse(savedDarkMode);
      setDarkMode(isDarkMode);
    }
  }, []);

  useEffect(() => {
    // Save dark mode preference to localStorage
    localStorage.setItem('darkMode', JSON.stringify(darkMode));
    
    // Apply dark mode class to body
    document.body.classList.toggle('dark-mode', darkMode);
  }, [darkMode]);

  const toggleDarkMode = () => {
    setDarkMode(prev => !prev);
  };

  return { darkMode, toggleDarkMode };
};
</file>

<file path="hooks/useStreaming.ts">
import { useCallback } from 'react';
import { ChatMessage } from '../types';

interface StreamingState {
    streamedText: string;
    isReceivingImage: boolean;
    imageBuffer: string;
    pendingImages: string[];
    hasReceivedFirstContent: boolean;
    currentTool: string;
    responseStarted: boolean;
    hasReceivedFirstToolCall: boolean;
}

interface UseStreamingOptions {
    setMessages: React.Dispatch<React.SetStateAction<ChatMessage[]>>;
    setThreadId: (id: string | null) => void;
}

export const useStreaming = ({ setMessages, setThreadId }: UseStreamingOptions) => {

    const processStreamingResponse = useCallback(async (response: Response) => {
        if (!response.body) {
            throw new Error('No response body for streaming');
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();

        // Initialize streaming state
        const state: StreamingState = {
            streamedText: '',
            isReceivingImage: false,
            imageBuffer: '',
            pendingImages: [],
            hasReceivedFirstContent: false,
            currentTool: '',
            responseStarted: false,
            hasReceivedFirstToolCall: false,
        };

        // Process the stream
        while (true) {
            const { value, done } = await reader.read();
            if (done) break;

            const chunk = decoder.decode(value, { stream: true });

            // Handle different chunk types, returns true if it was a metadata chunk.
            if (await handleChunk(chunk, state)) {
                continue;
            }
        }
        // Final cleanup
        await handleStreamEnd(state);

    }, [setMessages, setThreadId]);

    const handleChunk = useCallback(async (chunk: string, state: StreamingState): Promise<boolean> => {

        // Always started with a thread ID.
        if (chunk.includes('[STARTED THREAD]:')) {
            const parts = chunk.split(':');
            if (parts.length > 1) {
                const newThreadId = parts[1].trim();
                setThreadId(newThreadId);
                console.log('Set new threadId:', newThreadId);
            }
            return true;
        }

        // Handle tool calls
        if (!state.responseStarted && chunk.includes('[TOOLCALL]')) {
            handleToolCalls(chunk, state);
            return true;
        }

        // Handle response start
        if (!state.responseStarted && chunk.includes('[STARTED RESPONSE]:')) {
            handleResponseStart(chunk, state);
            return true;
        }

        // Handle images.
        if (chunk.includes('[IMAGE]:') || state.isReceivingImage) {
            handleImageChunk(chunk, state);
            return true;
        }

        // Handle content chunks only after response has started
        if (state.responseStarted) {
            handleContentChunk(chunk, state);
        }

        return false;
    }, [setMessages, setThreadId]);

    const handleToolCalls = useCallback((chunk: string, state: StreamingState) => {
        const toolCallRegex = /\[TOOLCALL\]\s*([^[]+?)\s*\[ENDTOOLCALL\]/g;
        let match;

        while ((match = toolCallRegex.exec(chunk)) !== null) {
            const toolName = match[1].trim();
            if (toolName) {
                state.currentTool = toolName;
                var typingInfo = state.currentTool;

                if (toolName === 'visualization-agent') {
                    typingInfo = 'Using the Visualization Agent to generate a nice looking chart, this can take a few seconds...';
                } else if (toolName === 'scenario-agent') {
                    typingInfo = 'Using the Scenario Agent to analyze the current scenario, please hold on...';
                } else if (toolName === 'installment-rule-evaluation-agent') {
                    typingInfo = 'Using the Installment Rule Evaluation Agent to evaluate the possibilities, please give me a few seconds...';
                }

                // Update typing indicator message with tool calls
                if (!state.hasReceivedFirstToolCall) {
                    state.hasReceivedFirstToolCall = true;
                    setMessages(prev => [
                        ...prev.slice(0, -1), // Remove original typing indicator
                        {
                            sender: 'bot',
                            text: '',
                            images: [],
                            typingInfo: typingInfo,
                            isTyping: true // Keep as typing message but with tool calls
                        }
                    ]);
                } else {
                    setMessages(prev => {
                        const updated = [...prev];
                        updated[updated.length - 1] = {
                            sender: 'bot',
                            text: '',
                            images: [],
                            typingInfo: typingInfo,
                            isTyping: true // Keep as typing message
                        };
                        return updated;
                    });

                }
            }
        }
    }, [setMessages]);

    const handleResponseStart = useCallback((chunk: string, state: StreamingState) => {
        state.responseStarted = true;

        // Extract the content after [STARTED RESPONSE]: 
        const responseRegex = /\[STARTED RESPONSE\]:\s*(.*)/;
        const responseMatch = chunk.match(responseRegex);
        if (responseMatch && responseMatch[1]) {
            const firstContentChunk = responseMatch[1];
            console.log('First content chunk from STARTED RESPONSE:', firstContentChunk);

            // Process this first chunk as regular content
            if (firstContentChunk.trim()) {
                // Remove typing indicator on first content
                if (!state.hasReceivedFirstContent) {
                    state.hasReceivedFirstContent = true;
                    setMessages(prev => [
                        ...prev.slice(0, -1), // Remove typing indicator
                        {
                            sender: 'bot',
                            text: '',
                            images: [],
                            typingInfo: state.currentTool,
                            isTyping: false
                        }
                    ]);
                }

                state.streamedText += firstContentChunk;

                // Update the message with the first content
                updateBotMessage(state);
            }
        }
    }, [setMessages]);

    const handleContentChunk = useCallback((chunk: string, state: StreamingState) => {
        // Handle image chunks
        if (chunk.includes('[IMAGE]:') || state.isReceivingImage) {
            handleImageChunk(chunk, state);
        } else {
            // Regular text chunk
            handleTextChunk(chunk, state);
        }
    }, []);

    const handleImageChunk = useCallback((chunk: string, state: StreamingState) => {
        // Remove typing indicator on first content
        if (!state.hasReceivedFirstContent) {
            state.hasReceivedFirstContent = true;
            setMessages(prev => [
                ...prev.slice(0, -1), // Remove typing indicator
                {
                    sender: 'bot',
                    text: '',
                    images: [],
                    typingInfo: state.currentTool,
                    isTyping: false
                }
            ]);
        }

        // Start of a new image
        if (chunk.includes('[IMAGE]:') && !state.isReceivingImage) {
            const imageStartIndex = chunk.indexOf('[IMAGE]:');
            const textBeforeImage = chunk.substring(0, imageStartIndex);
            const imageDataStart = chunk.substring(imageStartIndex + '[IMAGE]:'.length);

            // Add any text before the image marker
            if (textBeforeImage) {
                state.streamedText += textBeforeImage;
            }

            // Start collecting image data
            state.isReceivingImage = true;
            state.imageBuffer = imageDataStart;

        } else if (state.isReceivingImage) {
            // All chunks are part of the image until [IMAGE_END]
            state.imageBuffer += chunk;
        }

        // Check if image is complete with [IMAGE_END] marker
        if (state.isReceivingImage && chunk.includes('[IMAGE_END]')) {
            const endIndex = state.imageBuffer.indexOf('[IMAGE_END]');

            if (endIndex !== -1) {
                // Extract just the image data (before [IMAGE_END])
                const finalImageData = state.imageBuffer.substring(0, endIndex);

                if (finalImageData.trim()) {
                    state.pendingImages.push(finalImageData.trim());
                }

                // Extract any text after [IMAGE_END]
                const remainingText = state.imageBuffer.substring(endIndex + '[IMAGE_END]'.length);
                if (remainingText) {
                    state.streamedText += remainingText;
                }

                state.imageBuffer = '';
                state.isReceivingImage = false;
            }
        }

        // Update message with current text and images
        updateBotMessage(state);
    }, [setMessages]);

    const handleTextChunk = useCallback((chunk: string, state: StreamingState) => {
        // Remove typing indicator on first content
        if (!state.hasReceivedFirstContent) {
            state.hasReceivedFirstContent = true;
            setMessages(prev => [
                ...prev.slice(0, -1), // Remove typing indicator
                {
                    sender: 'bot',
                    text: '',
                    images: [],
                    typingInfo: state.currentTool,
                    isTyping: false
                }
            ]);
        }

        state.streamedText += chunk;

        updateBotMessage(state);
    }, [setMessages]);


    const updateBotMessage = useCallback((state: StreamingState) => {
        setMessages(prev => {
            const updated = [...prev];
            updated[updated.length - 1] = {
                sender: 'bot',
                text: state.streamedText,
                images: [...state.pendingImages],
                typingInfo: state.currentTool,
                isTyping: false
            };
            return updated;
        });
    }, [setMessages]);

    const handleStreamEnd = useCallback(async (state: StreamingState) => {
        // Handle any remaining image data when stream ends
        if (state.isReceivingImage && state.imageBuffer.trim()) {
            state.pendingImages.push(state.imageBuffer.trim());
        }

        // Final update with all collected data
        if (state.pendingImages.length > 0 || state.streamedText.trim()) {
            updateBotMessage(state);
        } else {
            // If no content was streamed, show error
            setMessages(prev => [
                ...prev.slice(0, -1), // Remove empty bot message
                {
                    sender: 'bot',
                    text: 'Er is een fout opgetreden. Probeer het later opnieuw.',
                    images: [],
                    typingInfo: state.currentTool,
                    isTyping: false
                }
            ]);
        }
    }, [setMessages, updateBotMessage]);

    return {
        processStreamingResponse
    };
};
</file>

<file path="services/ChatApiService.ts">
import { ChatApiRequest, ChatApiResponse } from '../types';

const API_ENDPOINT = (process.env.REACT_APP_CHAT_API || 'https://localhost:55018').replace(/\/+$/, '') + '/chat';

export class ChatApiService {
  static async sendMessage(request: ChatApiRequest): Promise<ChatApiResponse | Response> {
    const response = await fetch(API_ENDPOINT, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // If streaming is enabled, return the response directly for stream processing
    if (request.stream) {
      return response;
    }

    return await response.json();
  }
}
</file>

<file path="types/index.ts">
export interface ChatMessage {
  sender: 'user' | 'bot';
  text: string;
  images?: string[];
  isTyping?: boolean;
  typingInfo?: string;
}

export interface ScenarioCard {
  text: string;
}

export interface ChatApiRequest {
  UserID: string;
  message: string;
  threadId?: string;
  stream?: boolean;
}

export interface ChatApiResponse {
  reply?: string;
  message?: string;
  threadId?: string;
  images?: string[];
}
</file>

<file path="App.tsx">
import { makeStyles, tokens } from '@fluentui/react-components';
import { TopBar, ChatMessages, ChatInput, ScenarioCards } from './components';
import { useChat } from './hooks/useChat';
import { ChatHeader } from 'components/ChatHeader';
import { InfoSection } from 'components/InfoSection';

const useStyles = makeStyles({
    container: {
        display: 'flex',
        flexDirection: 'column',
        height: '100vh',
        width: '100vw',
        backgroundColor: tokens.colorNeutralBackground1,
    },
    main: {
        display: 'flex',
        flexDirection: 'column',
        flex: 1,
        overflow: 'hidden',
        '@media (min-width: 1024px)': {
            flexDirection: 'row',
        },
    },
    chatSection: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: tokens.colorNeutralBackground2,
        flex: 5,
        overflow: 'hidden',
        maxWidth: '100%',
        '@media (min-width: 1024px)': {
            maxWidth: 'calc(100% - 350px)',
        },
    },
    chatContainer: {
        display: 'flex',
        margin: 'auto',
        flexDirection: 'column',
        flex: 1,
        backgroundColor: tokens.colorNeutralBackground1,
        borderRadius: tokens.borderRadiusXLarge,
        boxShadow: tokens.shadow4,
        overflow: 'hidden',
        height: '80%',
        width: '100%',
        maxWidth: '800px'
    }
});

interface AppProps {
    darkMode: boolean;
    toggleDarkMode: () => void;
}

export function App({ darkMode, toggleDarkMode }: AppProps) {
    const styles = useStyles();
    const {
        messages,
        loading,
        userId,
        streaming,
        scenarioCards,
        hasUserChatted,
        sendMessage,
        startNewChat,
        resetUserId,
        toggleStreaming,
    } = useChat(); return (
        <div className={styles.container}>
            <TopBar
                darkMode={darkMode}
                userId={userId}
                onToggleDarkMode={toggleDarkMode}
                onUserIdChange={resetUserId}
                streaming={streaming}
                onToggleStreaming={toggleStreaming}
            />
            <div className={styles.main}>
                <InfoSection/>
                <div className={styles.chatSection}>
                    <div className={styles.chatContainer}>
                        <ChatHeader
                            onNewChat={startNewChat}
                        />
                        <ChatMessages messages={messages} streaming={streaming} />
                        {!hasUserChatted && (
                            <ScenarioCards
                                scenarios={scenarioCards}
                                onScenarioClick={sendMessage}
                                loading={loading}
                            />
                        )}
                        <ChatInput loading={loading} onSendMessage={sendMessage} />
                    </div>
                </div>
            </div>
        </div>
    );
}
</file>

<file path="index.tsx">
import React from 'react';
import { createRoot } from 'react-dom/client';
import { FluentProvider, BrandVariants, createLightTheme, createDarkTheme, Theme } from '@fluentui/react-components';
import { App } from './App';
import './index.css';
import { useDarkMode } from 'hooks/useDarkMode';


// CHANGEME: theming of the app.
const demoTheme: BrandVariants = {
  10: "#020305",
  20: "#111723",
  30: "#16263D",
  40: "#193253",
  50: "#1B3F6A",
  60: "#1B4C82",
  70: "#18599B",
  80: "#1267B4",
  90: "#3174C2",
  100: "#4F82C8",
  110: "#6790CF",
  120: "#7D9ED5",
  130: "#92ACDC",
  140: "#A6BAE2",
  150: "#BAC9E9",
  160: "#CDD8EF"
};
const lightTheme: Theme = {
  ...createLightTheme(demoTheme),
};

const darkTheme: Theme = {
  ...createDarkTheme(demoTheme),
};


const Root: React.FC = () => {
  const { darkMode, toggleDarkMode } = useDarkMode();

  return (
    <FluentProvider theme={darkMode ? darkTheme : lightTheme}>
      <App darkMode={darkMode} toggleDarkMode={toggleDarkMode} />
    </FluentProvider>
  );
};

const container = document.getElementById('root');
if (!container) {
  throw new Error("Root container missing in index.html");
}
const root = createRoot(container);

root.render(<Root />);
</file>

</files>
