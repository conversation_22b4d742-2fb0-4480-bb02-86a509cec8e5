import { useEffect } from 'react';
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import type { ThemeMode} from '../types/chat';
import { THEME_MODE } from '../types/chat';

// Theme Store Interface
interface ThemeStore {
  // State
  mode: ThemeMode;
  systemPreference: 'light' | 'dark';
  
  // Actions
  setTheme: (mode: ThemeMode) => void;
  toggleTheme: () => void;
  setSystemPreference: (preference: 'light' | 'dark') => void;
  
  // Computed
  getEffectiveTheme: () => 'light' | 'dark';
}

// Detect system theme preference
const getSystemTheme = (): 'light' | 'dark' => {
  if (typeof window === 'undefined') return 'light';
  
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
};

// Apply theme to document
const applyTheme = (theme: 'light' | 'dark') => {
  if (typeof document === 'undefined') return;
  
  const root = document.documentElement;
  
  if (theme === 'dark') {
    root.setAttribute('data-theme', 'dark');
  } else {
    root.removeAttribute('data-theme');
  }
  
  // Also update the class for compatibility
  root.classList.toggle('dark', theme === 'dark');
};

// Create the theme store
export const useThemeStore = create<ThemeStore>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        mode: THEME_MODE.SYSTEM,
        systemPreference: getSystemTheme(),

        // Actions
        setTheme: (mode) => {
          set({ mode });
          
          // Apply the theme immediately
          const effectiveTheme = get().getEffectiveTheme();
          applyTheme(effectiveTheme);
        },

        toggleTheme: () => {
          const currentMode = get().mode;
          const effectiveTheme = get().getEffectiveTheme();
          
          let newMode: ThemeMode;
          
          if (currentMode === THEME_MODE.SYSTEM) {
            // If currently system, toggle to opposite of current system preference
            newMode = effectiveTheme === 'light' ? THEME_MODE.DARK : THEME_MODE.LIGHT;
          } else {
            // If currently light or dark, toggle to the opposite
            newMode = currentMode === THEME_MODE.LIGHT ? THEME_MODE.DARK : THEME_MODE.LIGHT;
          }
          
          get().setTheme(newMode);
        },

        setSystemPreference: (systemPreference) => {
          set({ systemPreference });
          
          // If currently using system theme, apply the new preference
          const currentMode = get().mode;
          if (currentMode === THEME_MODE.SYSTEM) {
            applyTheme(systemPreference);
          }
        },

        // Computed getters
        getEffectiveTheme: () => {
          const { mode, systemPreference } = get();
          
          switch (mode) {
            case THEME_MODE.LIGHT:
              return 'light';
            case THEME_MODE.DARK:
              return 'dark';
            case THEME_MODE.SYSTEM:
            default:
              return systemPreference;
          }
        },
      }),
      {
        name: 'theme-store',
        // Only persist the theme mode, not system preference
        partialize: (state) => ({
          mode: state.mode,
        }),
      }
    ),
    {
      name: 'theme-store',
    }
  )
);

// Initialize theme on store creation
const initializeTheme = () => {
  const store = useThemeStore.getState();
  const effectiveTheme = store.getEffectiveTheme();
  applyTheme(effectiveTheme);
};

// Listen for system theme changes
const setupSystemThemeListener = () => {
  if (typeof window === 'undefined') return;
  
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
  
  const handleChange = (e: MediaQueryListEvent) => {
    const newSystemPreference = e.matches ? 'dark' : 'light';
    useThemeStore.getState().setSystemPreference(newSystemPreference);
  };
  
  // Modern browsers
  if (mediaQuery.addEventListener) {
    mediaQuery.addEventListener('change', handleChange);
  } else {
    // Fallback for older browsers
    mediaQuery.addListener(handleChange);
  }
  
  // Return cleanup function
  return () => {
    if (mediaQuery.removeEventListener) {
      mediaQuery.removeEventListener('change', handleChange);
    } else {
      mediaQuery.removeListener(handleChange);
    }
  };
};

// Selectors for better performance
export const useThemeMode = () => useThemeStore((state) => state.mode);
export const useEffectiveTheme = () => useThemeStore((state) => state.getEffectiveTheme());
export const useSystemPreference = () => useThemeStore((state) => state.systemPreference);
export const useThemeActions = () => useThemeStore((state) => ({
  setTheme: state.setTheme,
  toggleTheme: state.toggleTheme,
  setSystemPreference: state.setSystemPreference,
}));

// Hook for theme initialization
export const useThemeInitialization = () => {
  useEffect(() => {
    // Initialize theme
    initializeTheme();
    
    // Setup system theme listener
    const cleanup = setupSystemThemeListener();
    
    // Update system preference on mount
    const currentSystemPreference = getSystemTheme();
    useThemeStore.getState().setSystemPreference(currentSystemPreference);
    
    return cleanup;
  }, []);
};

// Helper functions
export const getThemeIcon = (mode: ThemeMode): string => {
  switch (mode) {
    case THEME_MODE.LIGHT:
      return '☀️';
    case THEME_MODE.DARK:
      return '🌙';
    case THEME_MODE.SYSTEM:
    default:
      return '💻';
  }
};

export const getThemeLabel = (mode: ThemeMode): string => {
  switch (mode) {
    case THEME_MODE.LIGHT:
      return 'Light';
    case THEME_MODE.DARK:
      return 'Dark';
    case THEME_MODE.SYSTEM:
    default:
      return 'System';
  }
};

export const getNextTheme = (currentMode: ThemeMode): ThemeMode => {
  switch (currentMode) {
    case THEME_MODE.LIGHT:
      return THEME_MODE.DARK;
    case THEME_MODE.DARK:
      return THEME_MODE.SYSTEM;
    case THEME_MODE.SYSTEM:
    default:
      return THEME_MODE.LIGHT;
  }
};

// CSS custom property helpers
export const getCSSCustomProperty = (property: string): string => {
  if (typeof document === 'undefined') return '';

  return getComputedStyle(document.documentElement)
    .getPropertyValue(property)
    .trim();
};

export const setCSSCustomProperty = (property: string, value: string): void => {
  if (typeof document === 'undefined') return;

  document.documentElement.style.setProperty(property, value);
};

// Design System Color Helpers
export const getDesignSystemColor = (colorName: string, fallback: string = ''): string => {
  const cssProperty = `--color-${colorName.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
  return getCSSCustomProperty(cssProperty) || fallback;
};

// Get theme-aware colors
export const getThemeColors = () => {
  const effectiveTheme = useThemeStore.getState().getEffectiveTheme();

  return {
    // Background Colors
    backgroundPrimary: getCSSCustomProperty('--color-background-primary'),
    backgroundSecondary: getCSSCustomProperty('--color-background-secondary'),
    backgroundTertiary: getCSSCustomProperty('--color-background-tertiary'),

    // Text Colors
    textPrimary: getCSSCustomProperty('--color-text-primary'),
    textSecondary: getCSSCustomProperty('--color-text-secondary'),
    textBrand: getCSSCustomProperty('--color-text-brand'),

    // Brand Colors
    primary: getCSSCustomProperty('--color-primary'),
    secondary: getCSSCustomProperty('--color-secondary'),

    // Feedback Colors
    success: getCSSCustomProperty('--color-success'),
    error: getCSSCustomProperty('--color-error'),
    warning: getCSSCustomProperty('--color-warning'),

    // Chat Colors
    chatUserBg: getCSSCustomProperty('--color-chat-user-bg'),
    chatAgentBg: getCSSCustomProperty('--color-chat-agent-bg'),
    chatUserText: getCSSCustomProperty('--color-chat-user-text'),
    chatAgentText: getCSSCustomProperty('--color-chat-agent-text'),

    // Status Colors
    statusConnected: getCSSCustomProperty('--color-status-connected'),
    statusConnecting: getCSSCustomProperty('--color-status-connecting'),
    statusDisconnected: getCSSCustomProperty('--color-status-disconnected'),
    statusError: getCSSCustomProperty('--color-status-error'),

    // Current theme
    scheme: effectiveTheme,
  };
};

// Apply design system colors programmatically
export const applyDesignSystemColors = (colors: Record<string, string>) => {
  if (typeof document === 'undefined') return;

  Object.entries(colors).forEach(([key, value]) => {
    const cssProperty = `--color-${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
    setCSSCustomProperty(cssProperty, value);
  });
};
