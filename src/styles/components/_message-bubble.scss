// Message Bubble Component Styles

.message-bubble {
  display: flex;
  margin-bottom: var(--chat-message-spacing);
  max-width: 80%;
  flex-direction: column;
  @include fade-in;
  min-width: 100px;
  padding-bottom: 4px;
  
  &--own {
    align-self: flex-end;
    justify-content: flex-end;

    .message-bubble__content {
      // Use design system neutral color for user messages (neutral-900)
      background: var(--color-text-primary);
      color: white;
      @include rounded-l(lg);
      @include rounded-t(lg);
      @include rounded-br(sm);
      @include shadow(sm);
    }
  }

  &--has-images {
    max-width: 85%;
  }

  &--other {
    align-self: flex-start;
    justify-content: flex-start;

    .message-bubble__content {
      // Use design system colors for agent messages
      background-color: var(--color-chat-agent-bg);
      color: var(--color-chat-agent-text);
      border: 1px solid var(--color-border);
      @include rounded-r(lg);
      @include rounded-t(lg);
      @include rounded-bl(sm);
      @include shadow(sm);
    }
  }
  
  &--sending {
    opacity: 0.7;

    .message-bubble__content {
      position: relative;
      
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        right: var(--spacing-3);
        transform: translateY(-50%);
        width: 12px;
        height: 12px;
        border: 2px solid currentColor;
        border-top-color: transparent;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }
  }
  
  &--error {
    .message-bubble__content {
      // Use design system feedback colors for error state
      border: 1px solid var(--color-error);
      background-color: var(--color-feedback-bg-error);
      color: var(--color-error);
    }
  }
}

// Streaming-specific styles
.message-bubble__tool-info {
  @include padding-y(2);
  @include padding-x(3);
  background-color: var(--color-chat-tool-bg, rgba(59, 130, 246, 0.1));
  color: var(--color-chat-tool-text, #3b82f6);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  border: 1px solid var(--color-chat-tool-border, rgba(59, 130, 246, 0.2));
}

.message-bubble__tool-icon {
  font-size: var(--font-size-sm);
  animation: pulse 2s infinite;
}

.message-bubble__text--streaming {
  position: relative;
}

.message-bubble__streaming-cursor {
  display: inline-block;
  animation: blink 1s infinite;
  color: var(--color-primary);
  font-weight: bold;
  margin-left: 2px;
}

// Animations
@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.message-bubble__content {
  @include padding(3);
  @include shadow(sm);
  @include transition();
  position: relative;
  word-wrap: break-word;
  overflow-wrap: break-word;
  
  &:hover {
    @include shadow(md);
  }
}

.message-bubble__text {
  @include chat-message;
  margin: 0;
  
  // Handle long words and URLs
  word-break: break-word;
  hyphens: auto;
  
  // Preserve whitespace and line breaks
  white-space: pre-wrap;
  
  // Style links if any
  a {
    color: inherit;
    text-decoration: underline;
    
    &:hover {
      text-decoration: none;
    }
  }
  
  // Style code blocks with design system colors
  code {
    background-color: var(--color-background-secondary);
    color: var(--color-text-secondary);
    padding: var(--spacing-1);
    @include rounded(sm);
    font-family: var(--font-family-mono);
    @include text-sm;
  }

  pre {
    background-color: var(--color-background-secondary);
    color: var(--color-text-primary);
    @include padding(2);
    @include rounded(md);
    overflow-x: auto;
    margin: var(--spacing-2) 0;
    border: 1px solid var(--color-border);

    code {
      background: none;
      padding: 0;
      color: inherit;
    }
  }
}

.message-bubble__images {
  margin-top: var(--spacing-sm);

  // Ensure images don't break the bubble layout
  .image-gallery {
    max-width: 100%;
  }
}

.message-bubble__timestamp {
  @include chat-timestamp;
  margin-top: var(--spacing-1);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-1);
  opacity: 0.8;
  width: 100%;
  text-align: right;
}

.message-bubble__status {
  @include text-xs;
  font-style: italic;
}

.message-bubble__error {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  margin-top: var(--spacing-2);
  @include text-xs;
  color: rgba(255, 255, 255, 0.9);
  
  svg {
    flex-shrink: 0;
  }
}

.message-bubble__retry {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  @include padding(1);
  @include rounded(sm);
  @include transition();
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }
  
  &:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5);
    outline-offset: 2px;
  }
  
  svg {
    @include transition();
  }
  
  &:hover svg {
    transform: rotate(180deg);
  }
}

// Typing indicator animation
@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

.message-bubble--typing {
  .message-bubble__content {
    @include padding-x(4);
    @include padding-y(3);
    
    &::after {
      content: '';
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: currentColor;
      animation: typing 1.4s infinite ease-in-out;
      margin-right: var(--spacing-1);
    }
    
    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: currentColor;
      animation: typing 1.4s infinite ease-in-out 0.2s;
      margin-right: var(--spacing-1);
    }
  }
  
  .message-bubble__text::after {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: currentColor;
    animation: typing 1.4s infinite ease-in-out 0.4s;
  }
}

// Responsive adjustments
@include mobile {
  .message-bubble {
    max-width: 90%;
    
    &__content {
      @include padding(2);
    }
    
    &__text {
      @include text-sm;
    }
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .message-bubble {
    &--own .message-bubble__content {
      border: 2px solid var(--color-text-primary);
    }
    
    &--other .message-bubble__content {
      border: 2px solid var(--color-text-secondary);
    }
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .message-bubble {
    animation: none;
    
    &--sending .message-bubble__content::after {
      animation: none;
      opacity: 0.5;
    }
    
    &--typing .message-bubble__content::after,
    &--typing .message-bubble__content::before,
    &--typing .message-bubble__text::after {
      animation: none;
    }
  }
  
  .message-bubble__retry:hover svg {
    transform: none;
  }
}
