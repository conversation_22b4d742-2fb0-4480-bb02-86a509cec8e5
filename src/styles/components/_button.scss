// Button Component Styles

.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  @include padding-x(4);
  @include padding-y(2);
  @include rounded(md);
  @include text-sm;
  font-weight: var(--font-weight-medium);
  @include transition();
  cursor: pointer;
  border: 1px solid transparent;
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  // Primary variant - Design System Aligned
  &--primary {
    background-color: var(--color-primary);
    color: var(--color-text-on-primary);

    &:hover:not(:disabled) {
      background-color: var(--color-primary-hover);
    }

    &:focus {
      @include shadow(lg);
      outline: 2px solid var(--color-primary-light);
      outline-offset: 2px;
    }
  }

  // Secondary variant - Design System Aligned
  &--secondary {
    background-color: var(--color-background-primary);
    color: var(--color-text-primary);
    border-color: var(--color-border);

    &:hover:not(:disabled) {
      background-color: var(--color-background-secondary);
      border-color: var(--color-border-hover);
    }

    &:focus {
      border-color: var(--color-primary);
      outline: 2px solid var(--color-primary-light);
      outline-offset: 2px;
    }
  }

  // Ghost variant - Design System Aligned
  &--ghost {
    background-color: transparent;
    color: var(--color-text-secondary);

    &:hover:not(:disabled) {
      background-color: var(--color-background-secondary);
      color: var(--color-text-primary);
    }

    &:focus {
      background-color: var(--color-background-tertiary);
      outline: 2px solid var(--color-primary-light);
      outline-offset: 2px;
    }
  }

  // Danger variant - Design System Aligned
  &--danger {
    background-color: var(--color-error);
    color: var(--color-text-on-primary);

    &:hover:not(:disabled) {
      background-color: var(--color-error);
      filter: brightness(0.9);
    }

    &:focus {
      outline: 2px solid var(--color-feedback-bg-error);
      outline-offset: 2px;
    }
  }
  
  // Size variants
  &--sm {
    @include padding-x(3);
    @include padding-y(1);
    @include text-xs;
  }
  
  &--lg {
    @include padding-x(6);
    @include padding-y(3);
    @include text-base;
  }
  
  // Icon only
  &--icon {
    @include padding(2);
    width: auto;
    height: auto;
    
    &.button--sm {
      @include padding(1);
    }
    
    &.button--lg {
      @include padding(3);
    }
  }
  
  // Full width
  &--full {
    width: 100%;
  }
  
  // Loading state
  &--loading {
    position: relative;
    color: transparent;
    
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 16px;
      height: 16px;
      border: 2px solid currentColor;
      border-top-color: transparent;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }
}

@keyframes spin {
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

// Floating Action Button
.fab {
  position: fixed;
  bottom: var(--spacing-6);
  right: var(--spacing-6);
  width: 56px;
  height: 56px;
  @include rounded(full);
  background-color: var(--color-primary);
  color: var(--color-text-on-primary);
  @include shadow(lg);
  border: none;
  cursor: pointer;
  @include transition();
  z-index: var(--z-index-fixed);
  
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    background-color: var(--color-primary-hover);
    @include shadow(xl);
    transform: scale(1.05);
  }
  
  &:active {
    transform: scale(0.95);
  }
  
  @include mobile {
    bottom: var(--spacing-4);
    right: var(--spacing-4);
    width: 48px;
    height: 48px;
  }
}
