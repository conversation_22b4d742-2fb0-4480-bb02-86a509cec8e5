// Chat Container Component Styles

.chat-container {
  display: flex;
  flex-direction: column;
  // Use design system background colors
  background: var(--color-background-primary);
  border: 1px solid var(--color-border);
  @include rounded(lg);
  @include shadow(lg);
  overflow: hidden;
  @include transition();
  
  &--chatbox {
    position: fixed;
    bottom: var(--spacing-6);
    right: var(--spacing-6);
    // Use viewport-filling dimensions like fullpage mode
    width: var(--floating-chat-size);
    height: 65vh; // Full height minus margins
    z-index: 9998; // Very high z-index to ensure visibility above all content (just below floating button)
    @include scale-in;

    // Enhanced modal appearance
    @include shadow(2xl);
    border: 2px solid var(--color-border);
    @include rounded(lg);

    // Ensure it's clearly above other content
    transform: translateZ(0); // Create new stacking context
    will-change: transform; // Optimize for animations

    // Smooth transition for size changes
    @include transition(width 0.3s ease, height 0.3s ease, max-height 0.3s ease);

    @include mobile {
      bottom: var(--spacing-4);
      right: var(--spacing-4);
      left: var(--spacing-4);
      top: var(--spacing-4);
      width: calc(100vw - var(--spacing-8));
      height: calc(100vh - var(--spacing-8));
    }

    // Expanded chatbox state - even larger dimensions while staying in bottom-right
    &.chat-container--expanded {
      // When expanded, take up even more space (reduce margins)
      width: calc(100vw - var(--spacing-6));
      height: calc(100vh - var(--spacing-6));
      bottom: var(--spacing-3);
      right: var(--spacing-3);

      @include mobile {
        bottom: var(--spacing-2);
        right: var(--spacing-2);
        left: var(--spacing-2);
        top: var(--spacing-2);
        width: calc(100vw - var(--spacing-4));
        height: calc(100vh - var(--spacing-4));
      }
    }
  }
  
  &--fullpage {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    height: 100vh;
    width: 100vw;
    border: none;
    border-radius: 0;
    box-shadow: none;
    z-index: 9997; // Very high z-index to ensure fullpage mode is above all background content
  }
}

.chat-container__header {
  @include padding(4);
  border-bottom: 1px solid var(--color-border);
  // Use design system brand colors (Eneco red/orange gradient) like other components
  background: linear-gradient(135deg, var(--color-eneco-red) 0%, var(--color-secondary) 100%);
  color: var(--color-text-on-primary);
  flex-shrink: 0;
  position: relative;
  @include shadow(sm);
}

.chat-container__header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.chat-container__title {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);

  h2 {
    @include heading-4;
    margin: 0;
    color: var(--color-text-on-primary);
    font-weight: var(--font-weight-semibold);
  }
}

.chat-container__avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  background: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  @include shadow(sm);
}

.chat-container__avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.chat-container__title-text {
  display: flex;
  flex-direction: column;
  gap: 2px;

  h2 {
    @include heading-4;
    margin: 0;
    color: var(--color-text-on-primary);
    line-height: 1.2;
    font-weight: var(--font-weight-semibold);
  }
}

.chat-container__subtitle {
  @include text-xs;
  color: rgba(255, 255, 255, 0.8);
  font-weight: var(--font-weight-medium);
}

.chat-container__header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.chat-container__action {
  @include padding(2);
  border: none;
  background: none;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  @include rounded(md);
  @include transition();
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
  }

  &:focus {
    outline: 2px solid white;
    outline-offset: 2px;
  }
  
  &--close {
    &:hover {
      background: var(--color-error);
      color: white;
    }
  }

  &--edwin-logo {
    padding: var(--spacing-1);
    border-radius: 50%;

    &:hover {
      background: var(--color-surface-hover);
      transform: scale(1.05);
    }
  }
}

.chat-container__edwin-logo {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}

.chat-container__unread-badge {
  position: absolute;
  top: var(--spacing-2);
  right: var(--spacing-2);
  background: var(--color-error);
  color: white;
  @include text-xs;
  font-weight: var(--font-weight-semibold);
  @include padding-x(2);
  @include padding-y(1);
  @include rounded(full);
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  @include scale-in;
}

.chat-container__content {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0; // Important for flex children with overflow
  position: relative; // Create stacking context
  z-index: 1; // Ensure content is above any background elements
}

.chat-container__messages {
  flex: 1;
  min-height: 0;
  overflow: hidden;
  position: relative; // Create stacking context
  z-index: 1; // Ensure messages are visible
}

.chat-container__input {
  flex-shrink: 0;
}

// Loading state
.chat-container--loading {
  .chat-container__content {
    opacity: 0.7;
    pointer-events: none;
  }
  
  .chat-container__header::after {
    content: '';
    position: absolute;
    top: 50%;
    right: var(--spacing-16);
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    border: 2px solid var(--color-border);
    border-top-color: var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

// Error state
.chat-container--error {
  border-color: var(--color-error);
  
  .chat-container__header {
    background: var(--color-error);
    color: white;
    
    .chat-container__title h2 {
      color: white;
    }
    
    .chat-container__action {
      color: rgba(255, 255, 255, 0.8);
      
      &:hover {
        background: rgba(255, 255, 255, 0.2);
        color: white;
      }
    }
  }
}

// Offline state
.chat-container--offline {
  .chat-container__header {
    background: var(--color-warning);
    color: white;
    
    .chat-container__title h2 {
      color: white;
    }
  }
  
  .chat-container__input {
    opacity: 0.6;
    pointer-events: none;
  }
}

// Focus trap for accessibility
.chat-container--floating,
.chat-container--fullscreen {
  &:focus-within {
    outline: 3px solid var(--color-primary);
    outline-offset: 2px;
  }
}

// Responsive adjustments
@include mobile {
  .chat-container__header {
    @include padding(3);
  }
  
  .chat-container__title h2 {
    @include text-lg;
  }
  
  .chat-container__action {
    @include padding(2);
    
    svg {
      width: 16px;
      height: 16px;
    }
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .chat-container {
    border-width: 2px;
  }
  
  .chat-container__header {
    border-bottom-width: 2px;
  }
  
  .chat-container__action {
    border: 1px solid transparent;
    
    &:hover {
      border-color: var(--color-text-primary);
    }
    
    &:focus {
      border-color: var(--color-primary);
    }
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .chat-container--floating,
  .chat-container--fullscreen {
    animation: none;
  }
  
  .chat-container__unread-badge {
    animation: none;
  }
  
  .chat-container--loading .chat-container__header::after {
    animation: none;
    opacity: 0.5;
  }
}

// Print styles
@media print {
  .chat-container {
    position: static !important;
    width: 100% !important;
    height: auto !important;
    max-height: none !important;
    border: 1px solid #000 !important;
    box-shadow: none !important;
    page-break-inside: avoid;
  }
  
  .chat-container__header-actions {
    display: none !important;
  }
}
