// Markdown Renderer Component Styles

.markdown-content {
  line-height: 1.6;
  color: inherit;

  // Preserve whitespace for plain text content
  &--plain {
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }

  // Streaming state
  &--streaming {
    // Smooth transitions for streaming content
    * {
      transition: opacity 0.1s ease-in-out;
    }
  }

  // Ensure all markdown elements inherit the message bubble's text color
  * {
    color: inherit;
  }

  // Override specific elements that need different colors
  .markdown-link {
    color: var(--color-primary);
  }

  .markdown-code-inline {
    color: var(--color-text-secondary);
  }

  .markdown-blockquote {
    color: var(--color-text-secondary);
  }
}

// Headers
.markdown-h1 {
  @include text-2xl;
  font-weight: var(--font-weight-bold);
  margin: var(--spacing-4) 0 var(--spacing-3) 0;
  color: inherit;
  border-bottom: 2px solid var(--color-border);
  padding-bottom: var(--spacing-2);
  
  &:first-child {
    margin-top: 0;
  }
}

.markdown-h2 {
  @include text-xl;
  font-weight: var(--font-weight-semibold);
  margin: var(--spacing-4) 0 var(--spacing-2) 0;
  color: inherit;
  border-bottom: 1px solid var(--color-border);
  padding-bottom: var(--spacing-1);
  
  &:first-child {
    margin-top: 0;
  }
}

.markdown-h3 {
  @include text-lg;
  font-weight: var(--font-weight-semibold);
  margin: var(--spacing-3) 0 var(--spacing-2) 0;
  color: inherit;
  
  &:first-child {
    margin-top: 0;
  }
}

.markdown-h4,
.markdown-h5,
.markdown-h6 {
  @include text-base;
  font-weight: var(--font-weight-medium);
  margin: var(--spacing-3) 0 var(--spacing-1) 0;
  color: inherit;
  
  &:first-child {
    margin-top: 0;
  }
}

// Paragraphs
.markdown-p {
  margin: var(--spacing-2) 0;
  color: inherit;
  
  &:first-child {
    margin-top: 0;
  }
  
  &:last-child {
    margin-bottom: 0;
  }
}

// Lists
.markdown-ul,
.markdown-ol {
  margin: var(--spacing-2) 0;
  padding-left: var(--spacing-4);
  color: inherit;
  
  &:first-child {
    margin-top: 0;
  }
  
  &:last-child {
    margin-bottom: 0;
  }
}

.markdown-li {
  margin: var(--spacing-1) 0;
  color: inherit;
  
  // Nested lists
  .markdown-ul,
  .markdown-ol {
    margin: var(--spacing-1) 0;
  }
}

// Code
.markdown-code-inline {
  background-color: var(--color-background-secondary);
  color: var(--color-text-secondary);
  padding: var(--spacing-1) var(--spacing-2);
  @include rounded(sm);
  font-family: var(--font-family-mono);
  @include text-sm;
  border: 1px solid var(--color-border);
}

.markdown-pre {
  background-color: var(--color-background-secondary);
  color: var(--color-text-primary);
  @include padding(3);
  @include rounded(md);
  overflow-x: auto;
  margin: var(--spacing-3) 0;
  border: 1px solid var(--color-border);
  
  &:first-child {
    margin-top: 0;
  }
  
  &:last-child {
    margin-bottom: 0;
  }
}

.markdown-code-block {
  background: none !important;
  padding: 0 !important;
  color: inherit !important;
  font-family: var(--font-family-mono);
  @include text-sm;
  line-height: 1.5;
}

// Links
.markdown-link {
  color: var(--color-primary);
  text-decoration: underline;
  @include transition();
  
  &:hover {
    color: var(--color-primary-hover);
    text-decoration: none;
  }
  
  &:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
    @include rounded(sm);
  }
}

// Emphasis
.markdown-strong {
  font-weight: var(--font-weight-bold);
  color: inherit;
}

.markdown-em {
  font-style: italic;
  color: inherit;
}

// Blockquotes
.markdown-blockquote {
  border-left: 4px solid var(--color-primary);
  @include padding-y(2);
  @include padding-x(3);
  margin: var(--spacing-3) 0;
  background-color: var(--color-background-secondary);
  color: var(--color-text-secondary);
  @include rounded-r(md);
  
  &:first-child {
    margin-top: 0;
  }
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .markdown-p {
    margin: var(--spacing-1) 0;
    
    &:first-child {
      margin-top: 0;
    }
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

// Tables
.markdown-table-wrapper {
  overflow-x: auto;
  margin: var(--spacing-3) 0;
  
  &:first-child {
    margin-top: 0;
  }
  
  &:last-child {
    margin-bottom: 0;
  }
}

.markdown-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid var(--color-border);
  @include rounded(md);
  overflow: hidden;
}

.markdown-thead {
  background-color: var(--color-background-secondary);
}

.markdown-th,
.markdown-td {
  @include padding(2);
  text-align: left;
  border-bottom: 1px solid var(--color-border);
  color: inherit;
}

.markdown-th {
  font-weight: var(--font-weight-semibold);
  background-color: var(--color-background-secondary);
  color: var(--color-text-primary);
}

.markdown-tr {
  &:last-child {
    .markdown-th,
    .markdown-td {
      border-bottom: none;
    }
  }
  
  &:nth-child(even) {
    background-color: var(--color-background-tertiary);
  }
}

// Horizontal rule
.markdown-hr {
  border: none;
  height: 1px;
  background-color: var(--color-border);
  margin: var(--spacing-4) 0;
  
  &:first-child {
    margin-top: 0;
  }
  
  &:last-child {
    margin-bottom: 0;
  }
}

// Images
.markdown-image {
  max-width: 100%;
  height: auto;
  @include rounded(md);
  margin: var(--spacing-2) 0;
  border: 1px solid var(--color-border);
  display: block;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  @include transition();

  &:first-child {
    margin-top: 0;
  }

  &:last-child {
    margin-bottom: 0;
  }

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
  }

  // Handle loading states
  &[src=""],
  &:not([src]) {
    display: none;
  }
}

// Responsive adjustments
@include mobile {
  .markdown-content {
    .markdown-h1 {
      @include text-xl;
    }
    
    .markdown-h2 {
      @include text-lg;
    }
    
    .markdown-h3 {
      @include text-base;
    }
    
    .markdown-ul,
    .markdown-ol {
      padding-left: var(--spacing-3);
    }
    
    .markdown-pre {
      @include padding(2);
      @include text-xs;
    }
    
    .markdown-table-wrapper {
      font-size: var(--font-size-sm);
    }
    
    .markdown-th,
    .markdown-td {
      @include padding(1);
    }

    .markdown-image {
      margin: var(--spacing-1) 0;
    }
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .markdown-content {
    .markdown-code-inline,
    .markdown-pre {
      border: 2px solid var(--color-text-primary);
    }
    
    .markdown-blockquote {
      border-left-width: 6px;
      border-left-color: var(--color-text-primary);
    }
    
    .markdown-table {
      border: 2px solid var(--color-text-primary);
    }
    
    .markdown-th,
    .markdown-td {
      border-bottom: 1px solid var(--color-text-primary);
    }
  }
}
