// Streaming Test Panel Styles (Development Only)

.streaming-test-panel {
  position: fixed;
  top: 20px;
  left: 20px;
  width: 320px;
  max-height: 80vh;
  overflow-y: auto;
  background: white;
  border: 2px solid var(--color-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  z-index: 9999;
  font-family: var(--font-family-base);
  
  &__header {
    padding: var(--spacing-4);
    border-bottom: 1px solid var(--color-border);
    background: var(--color-primary);
    color: white;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    
    h3 {
      margin: 0 0 var(--spacing-2) 0;
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
    }
  }
  
  &__status {
    label {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      font-size: var(--font-size-sm);
      cursor: pointer;
      
      input[type="checkbox"] {
        margin: 0;
      }
    }
  }
  
  &__scenarios {
    padding: var(--spacing-3);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
  }
  
  &__scenario {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-3);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius-md);
    background: var(--color-background-subtle);
    
    &:hover {
      background: var(--color-background-hover);
    }
  }
  
  &__scenario-info {
    flex: 1;
    
    h4 {
      margin: 0 0 var(--spacing-1) 0;
      font-size: var(--font-size-base);
      font-weight: var(--font-weight-medium);
      color: var(--color-text-primary);
    }
    
    p {
      margin: 0;
      font-size: var(--font-size-sm);
      color: var(--color-text-secondary);
      line-height: 1.4;
    }
  }
  
  &__test-button {
    padding: var(--spacing-2) var(--spacing-3);
    background: var(--color-primary);
    color: white;
    border: none;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 60px;
    
    &:hover:not(:disabled) {
      background: var(--color-primary-hover);
      transform: translateY(-1px);
    }
    
    &:active:not(:disabled) {
      transform: translateY(0);
    }
    
    &:disabled {
      background: var(--color-text-disabled);
      cursor: not-allowed;
      opacity: 0.6;
    }
  }
  
  &__info {
    padding: var(--spacing-3);
    border-top: 1px solid var(--color-border);
    background: var(--color-background-subtle);
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
    
    p {
      margin: 0 0 var(--spacing-2) 0;
      font-size: var(--font-size-xs);
      color: var(--color-text-secondary);
      line-height: 1.4;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      strong {
        color: var(--color-text-primary);
      }
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .streaming-test-panel {
    background: var(--color-background-dark, #1a1a1a);
    border-color: var(--color-primary);
    
    &__scenario {
      background: var(--color-background-subtle-dark, #2a2a2a);
      border-color: var(--color-border-dark, #404040);
      
      &:hover {
        background: var(--color-background-hover-dark, #333333);
      }
    }
    
    &__scenario-info {
      h4 {
        color: var(--color-text-primary-dark, #ffffff);
      }
      
      p {
        color: var(--color-text-secondary-dark, #cccccc);
      }
    }
    
    &__info {
      background: var(--color-background-subtle-dark, #2a2a2a);
      border-color: var(--color-border-dark, #404040);
      
      p {
        color: var(--color-text-secondary-dark, #cccccc);
        
        strong {
          color: var(--color-text-primary-dark, #ffffff);
        }
      }
    }
  }
}

// Mobile responsiveness
@media (max-width: 768px) {
  .streaming-test-panel {
    position: fixed;
    top: 10px;
    right: 10px;
    left: 10px;
    width: auto;
    max-height: 70vh;
  }
}
