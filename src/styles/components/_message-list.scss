// Message List Component Styles

.message-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  
  &--empty {
    justify-content: center;
    align-items: center;
  }
}

.message-list__container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  @include padding-x(4);
  @include padding-y(2);
  
  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  
  &::-webkit-scrollbar-thumb {
    background: var(--color-border);
    border-radius: var(--radius-full);
    
    &:hover {
      background: var(--color-border-hover);
    }
  }
}

.message-list__content {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  justify-content: flex-end;
}

.message-list__group {
  margin-bottom: var(--spacing-6);
  
  &:last-child {
    margin-bottom: 0;
  }
}

.message-list__date-header {
  text-align: center;
  margin-bottom: var(--spacing-4);
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--color-border);
    z-index: 1;
  }
  
  &::after {
    content: attr(data-date);
    background: var(--color-background);
    @include padding-x(3);
    @include text-xs;
    color: var(--color-text-muted);
    position: relative;
    z-index: 2;
  }
}

.message-list__date-header {
  @include text-xs;
  color: var(--color-text-muted);
  background: var(--color-background);
  @include padding-x(3);
  @include padding-y(1);
  @include rounded(full);
  border: 1px solid var(--color-border);
  display: inline-block;
  margin: 0 auto var(--spacing-4);
  position: relative;
  z-index: 2;
}

.message-list__messages {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.message-list__typing {
  margin-top: var(--spacing-2);
}

.message-list__end {
  height: 1px;
  width: 1px;
}

.message-list__scroll-to-bottom {
  position: absolute;
  bottom: var(--spacing-4);
  right: var(--spacing-4);
  width: 40px;
  height: 40px;
  @include rounded(full);
  background: var(--color-primary);
  color: white;
  border: none;
  cursor: pointer;
  @include shadow(lg);
  @include transition();
  z-index: 10;
  
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    background: var(--color-primary-hover);
    @include shadow(xl);
    transform: scale(1.05);
  }
  
  &:active {
    transform: scale(0.95);
  }
  
  @include fade-in;
}

// Empty state
.message-list__empty-state {
  text-align: center;
  @include padding(8);
  max-width: 300px;
}

.message-list__empty-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-4);
  opacity: 0.5;
}

.message-list__empty-title {
  @include heading-3;
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-2);
}

.message-list__empty-description {
  @include body-base;
  color: var(--color-text-secondary);
  margin: 0;
}

// Consecutive message styling
.message-bubble--consecutive {
  margin-top: var(--spacing-1);
  
  .message-bubble__content {
    margin-top: 0;
  }
  
  &.message-bubble--own .message-bubble__content {
    @include rounded-l(lg);
    @include rounded-tr(lg);
    @include rounded-br(sm);
  }
  
  &.message-bubble--other .message-bubble__content {
    @include rounded-r(lg);
    @include rounded-tl(lg);
    @include rounded-bl(sm);
  }
}

// Loading state
.message-list--loading {
  .message-list__container {
    opacity: 0.7;
    pointer-events: none;
  }
  
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 32px;
    height: 32px;
    border: 3px solid var(--color-border);
    border-top-color: var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

// Responsive adjustments
@include mobile {
  .message-list__container {
    @include padding-x(3);
  }
  
  .message-list__scroll-to-bottom {
    width: 36px;
    height: 36px;
    bottom: var(--spacing-3);
    right: var(--spacing-3);
  }
  
  .message-list__empty-state {
    @include padding(6);
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .message-list__date-header {
    border-width: 2px;
    font-weight: var(--font-weight-semibold);
  }
  
  .message-list__scroll-to-bottom {
    border: 2px solid var(--color-text-primary);
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .message-list__scroll-to-bottom {
    animation: none;
    
    &:hover {
      transform: none;
    }
    
    &:active {
      transform: none;
    }
  }
  
  .message-list--loading::after {
    animation: none;
    opacity: 0.5;
  }
}
