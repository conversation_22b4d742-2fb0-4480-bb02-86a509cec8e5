// Core chat types and interfaces

export type MessageSender = 'user' | 'agent';

export type MessageStatus = 'sending' | 'sent' | 'error';

export type ViewMode = 'fullpage' | 'chatbox' | 'minimized';

export type ThemeMode = 'light' | 'dark' | 'system';

// Chat Message Interface
export interface ChatMessage {
  id: string;
  content: string;
  sender: MessageSender;
  timestamp: Date;
  status: MessageStatus;
  error?: string; // Error message if status is 'error'
  isStreaming?: boolean; // Whether this message is currently being streamed
  isComplete?: boolean; // Whether streaming is complete for this message
  images?: string[]; // Optional array of base64-encoded images
  toolInfo?: string; // Information about tool calls during streaming
}

// Chat State Interface
export interface ChatState {
  messages: ChatMessage[];
  isTyping: boolean;
  viewMode: ViewMode;
  isMinimized: boolean;
  unreadCount: number;
  streamingEnabled: boolean; // Whether streaming is enabled
  currentStreamingMessageId?: string; // ID of message currently being streamed
}

// Theme State Interface
export interface ThemeState {
  mode: ThemeMode;
  systemPreference: 'light' | 'dark';
}

// API Request/Response Types
export interface SendMessageRequest {
  message: string;
  conversationId?: string;
  userId?: string;
}

export interface SendMessageResponse {
  id: string;
  response: string;
  conversationId: string;
  timestamp: string;
  status: 'success' | 'error';
  error?: string;
}

// Chat Configuration
export interface ChatConfig {
  apiEndpoint: string;
  maxRetries: number;
  retryDelay: number;
  typingIndicatorDelay: number;
  messageTimeout: number;
  enablePersistence: boolean;
  enableNotifications: boolean;
}

// User Interface
export interface User {
  id: string;
  name?: string;
  email?: string;
  avatar?: string;
}

// Conversation Interface
export interface Conversation {
  id: string;
  title?: string;
  createdAt: Date;
  updatedAt: Date;
  messageCount: number;
  lastMessage?: ChatMessage;
}

// Notification Interface
export interface ChatNotification {
  id: string;
  type: 'message' | 'connection' | 'error';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  actions?: NotificationAction[];
}

export interface NotificationAction {
  id: string;
  label: string;
  action: () => void;
}

// Form Types
export interface MessageFormData {
  message: string;
}

// Event Types
export type ChatEventType =
  | 'message_sent'
  | 'message_received'
  | 'typing_start'
  | 'typing_stop'
  | 'connection_change'
  | 'view_mode_change'
  | 'theme_change'
  | 'streaming_start'
  | 'streaming_chunk'
  | 'streaming_end'
  | 'streaming_error';

export interface ChatEvent {
  type: ChatEventType;
  payload: unknown;
  timestamp: Date;
}

// Error Types
export interface ChatError {
  code: string;
  message: string;
  details?: unknown;
  timestamp: Date;
  recoverable: boolean;
}

// API Error Response
export interface ApiErrorResponse {
  error: {
    code: string;
    message: string;
    details?: unknown;
  };
  timestamp: string;
}

// Utility Types
export type Partial<T> = {
  [P in keyof T]?: T[P];
};

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

// Component Props Types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface MessageBubbleProps extends BaseComponentProps {
  message: ChatMessage;
  isOwn: boolean;
  showTimestamp?: boolean;
  onRetry?: (messageId: string) => void;
}

export interface MessageInputProps extends BaseComponentProps {
  onSendMessage: (message: string) => Promise<void>;
  disabled?: boolean;
  placeholder?: string;
  maxLength?: number;
}

export interface ChatContainerProps extends BaseComponentProps {
  viewMode?: ViewMode;
  onViewModeChange?: (mode: ViewMode) => void;
  onMinimize?: () => void;
  onClose?: () => void;
}

export interface FloatingChatButtonProps extends BaseComponentProps {
  onClick: () => void;
  unreadCount?: number;
  isOnline?: boolean;
}

export interface ThemeToggleProps extends BaseComponentProps {
  theme: ThemeMode;
  onThemeChange: (theme: ThemeMode) => void;
}

// Store Types
export interface ChatStore extends ChatState {
  // Actions
  addMessage: (message: Omit<ChatMessage, 'id' | 'timestamp'>) => void;
  updateMessage: (id: string, updates: Partial<ChatMessage>) => void;
  removeMessage: (id: string) => void;
  clearMessages: () => void;
  setTyping: (isTyping: boolean) => void;
  setViewMode: (mode: ViewMode) => void;
  setMinimized: (minimized: boolean) => void;
  markAsRead: () => void;
  incrementUnreadCount: () => void;
}

export interface ThemeStore extends ThemeState {
  // Actions
  setTheme: (mode: ThemeMode) => void;
  toggleTheme: () => void;
  setSystemPreference: (preference: 'light' | 'dark') => void;
  getEffectiveTheme: () => 'light' | 'dark';
}

// Hook Return Types
export interface UseChatReturn {
  messages: ChatMessage[];
  sendMessage: (content: string) => Promise<void>;
  isTyping: boolean;
  retry: () => void;
  clearChat: () => void;
}

export interface UseConnectionReturn {
  connect: () => void;
  disconnect: () => void;
  retry: () => void;
  isConnected: boolean;
  isConnecting: boolean;
}

export interface UseThemeReturn {
  theme: ThemeMode;
  effectiveTheme: 'light' | 'dark';
  setTheme: (mode: ThemeMode) => void;
  toggleTheme: () => void;
}

// Constants
export const MESSAGE_STATUS = {
  SENDING: 'sending' as const,
  SENT: 'sent' as const,
  ERROR: 'error' as const,
} as const;


export const VIEW_MODE = {
  FULLPAGE: 'fullpage' as const,
  CHATBOX: 'chatbox' as const,
  MINIMIZED: 'minimized' as const,
} as const;

export const THEME_MODE = {
  LIGHT: 'light' as const,
  DARK: 'dark' as const,
  SYSTEM: 'system' as const,
} as const;
